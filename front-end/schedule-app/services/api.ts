import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_CONFIG, STORAGE_KEYS } from '../constants/AppConstants';
import { LoginForm, RegisterForm, User } from '../types';

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

interface AuthResponse {
  token: string;
  type: string;
  user: User;
}

class ApiService {
  private baseURL = API_CONFIG.baseURL;

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = await this.getAuthHeaders();

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
        timeout: API_CONFIG.timeout,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Erro na requisição');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Métodos de autenticação
  async login(credentials: LoginForm): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    return response.data;
  }

  async register(userData: RegisterForm): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        password: userData.password,
      }),
    });

    return response.data;
  }

  async logout(): Promise<void> {
    await this.request<void>('/auth/logout', {
      method: 'POST',
    });
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.request<User>('/auth/me');
    return response.data;
  }

  // Métodos de usuários
  async checkEmailExists(email: string): Promise<boolean> {
    const response = await this.request<boolean>(`/users/exists/email/${email}`);
    return response.data;
  }

  async checkPhoneExists(phone: string): Promise<boolean> {
    const response = await this.request<boolean>(`/users/exists/phone/${phone}`);
    return response.data;
  }

  // Métodos de serviços
  async getServices() {
    const response = await this.request('/services');
    return response.data;
  }

  async getServiceById(id: string) {
    const response = await this.request(`/services/${id}`);
    return response.data;
  }

  // Métodos de profissionais
  async getProfessionals() {
    const response = await this.request('/professionals');
    return response.data;
  }

  async getProfessionalById(id: string) {
    const response = await this.request(`/professionals/${id}`);
    return response.data;
  }

  async getProfessionalsByService(serviceId: string) {
    const response = await this.request(`/professionals/service/${serviceId}`);
    return response.data;
  }

  // Métodos de categorias
  async getCategories() {
    const response = await this.request('/categories');
    return response.data;
  }

  // Métodos de agendamentos
  async createAppointment(appointmentData: {
    userId: string;
    serviceId: string;
    professionalId: string;
    date: string;
    time: string;
    notes?: string;
  }) {
    const response = await this.request('/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
    return response.data;
  }

  async getUserAppointments(userId?: string) {
    const endpoint = userId ? `/appointments/user/${userId}` : '/appointments/user';
    const response = await this.request(endpoint);
    return response.data;
  }

  async getUpcomingAppointments(userId: string) {
    const response = await this.request(`/appointments/user/${userId}/upcoming`);
    return response.data;
  }

  async getAvailableTimeSlots(professionalId: string, date: string) {
    const response = await this.request(`/appointments/available-slots?professionalId=${professionalId}&date=${date}`);
    return response.data;
  }

  async cancelAppointment(appointmentId: string) {
    const response = await this.request(`/appointments/${appointmentId}/cancel`, {
      method: 'PATCH',
    });
    return response.data;
  }

  async updateAppointmentStatus(appointmentId: string, status: string) {
    const response = await this.request(`/appointments/${appointmentId}/status?status=${status}`, {
      method: 'PATCH',
    });
    return response.data;
  }

  async getAppointmentsByDate(date: string) {
    const response = await this.request(`/appointments/date/${date}`);
    return response.data;
  }

  async getAppointmentsByDateRange(startDate: string, endDate: string) {
    const response = await this.request(`/appointments/date-range?startDate=${startDate}&endDate=${endDate}`);
    return response.data;
  }

  async getAppointmentById(id: string) {
    const response = await this.request(`/appointments/${id}`);
    return response.data;
  }

  async updateAppointment(id: string, data: any) {
    const response = await this.request(`/appointments/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default ApiService;
