// Tipos principais do app de agendamento

export enum UserRole {
  CLIENT = 'client',
  ADMIN = 'admin',
  PROFESSIONAL = 'professional'
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export interface Service {
  id: string;
  name: string;
  description: string;
  duration: number; // em minutos
  price: number;
  category: ServiceCategory;
  image?: string;
  isActive: boolean;
}

export interface ServiceCategory {
  id: string;
  name: string;
  description?: string;
  icon: string;
  color: string;
}

export interface Professional {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  specialties: string[];
  isActive: boolean;
}

export interface Appointment {
  id: string;
  userId: string;
  serviceId: string;
  professionalId: string;
  date: Date;
  startTime: string;
  endTime: string;
  status: AppointmentStatus;
  notes?: string;
  totalPrice: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

export interface TimeSlot {
  time: string;
  isAvailable: boolean;
  professionalId?: string;
}

// Tipos para DTOs do backend
export interface TimeSlotDTO {
  time: string;
  available: boolean;
}

export interface AppointmentDTO {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  service: {
    id: string;
    name: string;
    duration: number;
    price: number;
  };
  professional: {
    id: string;
    name: string;
    email: string;
    specialties: string[];
  };
  date: string;
  startTime: string;
  endTime: string;
  status: AppointmentStatus;
  notes?: string;
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export interface BookingRequestDTO {
  userId: string;
  serviceId: string;
  professionalId: string;
  date: string;
  time: string;
  notes?: string;
}

export interface AvailableDay {
  date: Date;
  timeSlots: TimeSlot[];
}

// Tipos para navegação
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  ServiceDetails: { serviceId: string };
  Booking: { serviceId: string };
  AppointmentDetails: { appointmentId: string };
  Profile: undefined;
  EditProfile: undefined;
};

export type TabParamList = {
  Home: undefined;
  Services: undefined;
  Appointments: undefined;
  Profile: undefined;
};

// Tipos para formulários
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

export interface BookingForm {
  serviceId: string;
  professionalId: string;
  date: Date;
  time: string;
  notes?: string;
}

// Tipos para API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Tipos para relatórios administrativos
export interface DailyReport {
  date: Date;
  totalAppointments: number;
  totalRevenue: number;
  completedAppointments: number;
  cancelledAppointments: number;
  appointments: Appointment[];
}

export interface MonthlyReport {
  month: number;
  year: number;
  totalAppointments: number;
  totalRevenue: number;
  averageDailyRevenue: number;
  topServices: ServiceReport[];
  dailyReports: DailyReport[];
}

export interface ServiceReport {
  service: Service;
  totalBookings: number;
  totalRevenue: number;
  averageRating?: number;
}

export interface ProfessionalReport {
  professional: Professional;
  totalAppointments: number;
  totalRevenue: number;
  averageRating?: number;
  completionRate: number;
}

export interface AdminDashboard {
  todayStats: {
    appointments: number;
    revenue: number;
    newClients: number;
    completionRate: number;
  };
  weeklyStats: {
    appointments: number;
    revenue: number;
    growth: number;
  };
  monthlyStats: {
    appointments: number;
    revenue: number;
    growth: number;
  };
  recentAppointments: Appointment[];
  topServices: ServiceReport[];
  topProfessionals: ProfessionalReport[];
}