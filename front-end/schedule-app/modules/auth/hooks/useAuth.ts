import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useState } from 'react';
import { MESSAGES, STORAGE_KEYS } from '../../../constants/AppConstants';
import { LoginForm, RegisterForm, User, UserRole } from '../../../types';
// import { apiService } from '../../../services/api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  pendingServiceId: string | null; // Para redirecionamento após login
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
    pendingServiceId: null,
  });

  // Verificar se o usuário está logado ao inicializar
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.USER_TOKEN);
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);

      if (token && userData) {
        const user = JSON.parse(userData);
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
          pendingServiceId: null,
        });
      } else {
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          pendingServiceId: null,
        });
      }
    } catch (error) {
      console.error('Erro ao verificar status de autenticação:', error);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: MESSAGES.error.networkError,
        pendingServiceId: null,
      });
    }
  };

  const login = async (credentials: LoginForm): Promise<boolean> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Tentar login com API real primeiro
      try {
        // const authResponse = await apiService.login(credentials);

        // // Salvar dados no AsyncStorage
        // await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, authResponse.token);
        // await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user));

        // setAuthState({
        //   user: authResponse.user,
        //   isAuthenticated: true,
        //   isLoading: false,
        //   error: null,
        //   pendingServiceId: null,
        // });

        // return true;
        throw new Error('API temporarily disabled');
      } catch (apiError) {
        console.log('API não disponível, usando mock:', apiError);

        // Fallback para mock se API não estiver disponível
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Mock de validação
        if (credentials.email === '<EMAIL>' && credentials.password === '123456') {
          const mockUser: User = {
            id: '1',
            name: 'Usuário Teste',
            email: credentials.email,
            phone: '(11) 99999-9999',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
            role: UserRole.CLIENT,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const mockToken = 'mock-jwt-token-client';
          await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, mockToken);
          await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(mockUser));

          setAuthState({
            user: mockUser,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            pendingServiceId: null,
          });

          return true;
        } else if (credentials.email === '<EMAIL>' && credentials.password === 'admin123') {
          const mockUser: User = {
            id: 'admin1',
            name: 'Administrador',
            email: credentials.email,
            phone: '(11) 99999-0000',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
            role: UserRole.ADMIN,
            createdAt: new Date(),
            updatedAt: new Date(),
          };

          const mockToken = 'mock-jwt-token-admin';
          await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, mockToken);
          await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(mockUser));

          setAuthState({
            user: mockUser,
            isAuthenticated: true,
            isLoading: false,
            error: null,
            pendingServiceId: null,
          });

          return true;
        } else {
          setAuthState(prev => ({
            ...prev,
            isLoading: false,
            error: MESSAGES.error.invalidCredentials,
          }));
          return false;
        }
      }
    } catch (error) {
      console.error('Erro no login:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: MESSAGES.error.networkError,
      }));
      return false;
    }
  };

  const register = async (userData: RegisterForm): Promise<boolean> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Tentar registro com API real primeiro
      try {
        // const authResponse = await apiService.register(userData);

        // // Salvar dados no AsyncStorage
        // await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, authResponse.token);
        // await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user));

        // setAuthState({
        //   user: authResponse.user,
        //   isAuthenticated: true,
        //   isLoading: false,
        //   error: null,
        //   pendingServiceId: null,
        // });

        // return true;
        throw new Error('API temporarily disabled');
      } catch (apiError) {
        console.log('API não disponível, usando mock:', apiError);

        // Fallback para mock se API não estiver disponível
        await new Promise(resolve => setTimeout(resolve, 2000));

        const newUser: User = {
          id: Date.now().toString(),
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          role: UserRole.CLIENT,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockToken = 'mock-jwt-token-register';

        // Salvar dados no AsyncStorage
        await AsyncStorage.setItem(STORAGE_KEYS.USER_TOKEN, mockToken);
        await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(newUser));

        setAuthState({
          user: newUser,
          isAuthenticated: true,
          isLoading: false,
          error: null,
          pendingServiceId: null,
        });

        return true;
      }
    } catch (error) {
      console.error('Erro no registro:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: MESSAGES.error.networkError,
      }));
      return false;
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_TOKEN);
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);

      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        pendingServiceId: null,
      });
    } catch (error) {
      console.error('Erro no logout:', error);
    }
  };

  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }));
  };

  const setPendingService = (serviceId: string) => {
    setAuthState(prev => ({ ...prev, pendingServiceId: serviceId }));
  };

  const clearPendingService = () => {
    setAuthState(prev => ({ ...prev, pendingServiceId: null }));
  };

  return {
    ...authState,
    login,
    register,
    logout,
    clearError,
    setPendingService,
    clearPendingService,
  };
};