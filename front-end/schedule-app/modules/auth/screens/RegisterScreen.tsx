import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, Input } from '../../../components/common';
import { RegisterForm } from '../../../types';
import { COLORS, SIZES, FONTS, VALIDATION_RULES, MESSAGES } from '../../../constants/AppConstants';

interface RegisterScreenProps {
  onRegister: (userData: RegisterForm) => void;
  onNavigateToLogin: () => void;
  loading?: boolean;
}

export const RegisterScreen: React.FC<RegisterScreenProps> = ({
  onRegister,
  onNavigateToLogin,
  loading = false,
}) => {
  const [formData, setFormData] = useState<RegisterForm>({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<Partial<RegisterForm>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<RegisterForm> = {};

    // Validação do nome
    if (!formData.name.trim()) {
      newErrors.name = MESSAGES.error.requiredField;
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Nome deve ter pelo menos 2 caracteres.';
    }

    // Validação do email
    if (!formData.email.trim()) {
      newErrors.email = MESSAGES.error.requiredField;
    } else if (!VALIDATION_RULES.email.test(formData.email)) {
      newErrors.email = MESSAGES.error.invalidEmail;
    }

    // Validação do telefone
    if (!formData.phone.trim()) {
      newErrors.phone = MESSAGES.error.requiredField;
    } else if (!VALIDATION_RULES.phone.test(formData.phone)) {
      newErrors.phone = MESSAGES.error.invalidPhone;
    }

    // Validação da senha
    if (!formData.password.trim()) {
      newErrors.password = MESSAGES.error.requiredField;
    } else if (formData.password.length < VALIDATION_RULES.password.minLength) {
      newErrors.password = MESSAGES.error.passwordTooShort;
    }

    // Validação da confirmação de senha
    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = MESSAGES.error.requiredField;
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Senhas não coincidem.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = () => {
    if (validateForm()) {
      onRegister(formData);
    }
  };

  const updateField = (field: keyof RegisterForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const formatPhone = (text: string) => {
    // Remove tudo que não é número
    const numbers = text.replace(/\D/g, '');

    // Aplica a máscara (11) 99999-9999
    if (numbers.length <= 11) {
      const match = numbers.match(/^(\d{0,2})(\d{0,5})(\d{0,4})$/);
      if (match) {
        const formatted = `${match[1] ? `(${match[1]}` : ''}${match[1] && match[1].length === 2 ? ') ' : ''}${match[2]}${match[2] && match[3] ? '-' : ''}${match[3]}`;
        return formatted;
      }
    }
    return text;
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Criar Conta</Text>
            <Text style={styles.subtitle}>
              Preencha os dados para criar sua conta
            </Text>
          </View>

          <View style={styles.form}>
            <Input
              label="Nome completo"
              placeholder="Digite seu nome"
              value={formData.name}
              onChangeText={(text) => updateField('name', text)}
              leftIcon="person-outline"
              error={errors.name}
            />

            <Input
              label="Email"
              placeholder="Digite seu email"
              value={formData.email}
              onChangeText={(text) => updateField('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail-outline"
              error={errors.email}
            />

            <Input
              label="Telefone"
              placeholder="(11) 99999-9999"
              value={formData.phone}
              onChangeText={(text) => updateField('phone', formatPhone(text))}
              keyboardType="phone-pad"
              leftIcon="call-outline"
              error={errors.phone}
            />

            <Input
              label="Senha"
              placeholder="Digite sua senha"
              value={formData.password}
              onChangeText={(text) => updateField('password', text)}
              secureTextEntry
              leftIcon="lock-closed-outline"
              error={errors.password}
            />

            <Input
              label="Confirmar senha"
              placeholder="Digite sua senha novamente"
              value={formData.confirmPassword}
              onChangeText={(text) => updateField('confirmPassword', text)}
              secureTextEntry
              leftIcon="lock-closed-outline"
              error={errors.confirmPassword}
            />

            <Button
              title="Criar conta"
              onPress={handleRegister}
              loading={loading}
              style={styles.registerButton}
            />
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Já tem uma conta?</Text>
            <Button
              title="Fazer login"
              onPress={onNavigateToLogin}
              variant="text"
              style={styles.loginButton}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SIZES.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: SIZES.xl,
  },
  title: {
    fontSize: SIZES.fontTitle,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.sm,
  },
  subtitle: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    marginBottom: SIZES.xl,
  },
  registerButton: {
    marginTop: SIZES.lg,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginBottom: SIZES.xs,
  },
  loginButton: {
    marginTop: SIZES.xs,
  },
});