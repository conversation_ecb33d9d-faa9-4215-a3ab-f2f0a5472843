import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, Input } from '../../../components/common';
import { LoginForm } from '../../../types';
import { COLORS, SIZES, FONTS, VALIDATION_RULES, MESSAGES } from '../../../constants/AppConstants';

interface LoginScreenProps {
  onLogin: (credentials: LoginForm) => void;
  onNavigateToRegister: () => void;
  onForgotPassword: () => void;
  loading?: boolean;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({
  onLogin,
  onNavigateToRegister,
  onForgotPassword,
  loading = false,
}) => {
  const [formData, setFormData] = useState<LoginForm>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Partial<LoginForm>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<LoginForm> = {};

    // Validação do email
    if (!formData.email.trim()) {
      newErrors.email = MESSAGES.error.requiredField;
    } else if (!VALIDATION_RULES.email.test(formData.email)) {
      newErrors.email = MESSAGES.error.invalidEmail;
    }

    // Validação da senha
    if (!formData.password.trim()) {
      newErrors.password = MESSAGES.error.requiredField;
    } else if (formData.password.length < VALIDATION_RULES.password.minLength) {
      newErrors.password = MESSAGES.error.passwordTooShort;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = () => {
    if (validateForm()) {
      onLogin(formData);
    }
  };

  const updateField = (field: keyof LoginForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Limpar erro do campo quando o usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Bem-vindo!</Text>
            <Text style={styles.subtitle}>
              Entre na sua conta para agendar seus serviços
            </Text>
          </View>

          <View style={styles.form}>
            <Input
              label="Email"
              placeholder="Digite seu email"
              value={formData.email}
              onChangeText={(text) => updateField('email', text)}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon="mail-outline"
              error={errors.email}
            />

            <Input
              label="Senha"
              placeholder="Digite sua senha"
              value={formData.password}
              onChangeText={(text) => updateField('password', text)}
              secureTextEntry
              leftIcon="lock-closed-outline"
              error={errors.password}
            />

            <Button
              title="Entrar"
              onPress={handleLogin}
              loading={loading}
              style={styles.loginButton}
            />

            <Button
              title="Esqueci minha senha"
              onPress={onForgotPassword}
              variant="text"
              style={styles.forgotPasswordButton}
            />
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Não tem uma conta?</Text>
            <Button
              title="Criar conta"
              onPress={onNavigateToRegister}
              variant="text"
              style={styles.registerButton}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: SIZES.lg,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: SIZES.xxl,
  },
  title: {
    fontSize: SIZES.fontTitle,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.sm,
  },
  subtitle: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    marginBottom: SIZES.xl,
  },
  loginButton: {
    marginTop: SIZES.lg,
  },
  forgotPasswordButton: {
    marginTop: SIZES.md,
    alignSelf: 'center',
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginBottom: SIZES.xs,
  },
  registerButton: {
    marginTop: SIZES.xs,
  },
});