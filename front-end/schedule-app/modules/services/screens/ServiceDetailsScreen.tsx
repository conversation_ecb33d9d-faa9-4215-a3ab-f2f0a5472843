import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../../components/common';
import { Service } from '../../../types';
import { COLORS, SIZES, FONTS } from '../../../constants/AppConstants';

interface ServiceDetailsScreenProps {
  service: Service;
  onBookService: (service: Service) => void;
  onGoBack: () => void;
}

export const ServiceDetailsScreen: React.FC<ServiceDetailsScreenProps> = ({
  service,
  onBookService,
  onGoBack,
}) => {
  const formatPrice = (price: number) => {
    return `R$ ${price.toFixed(2).replace('.', ',')}`;
  };

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;

    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${minutes}min`;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={onGoBack}
        >
          <Ionicons
            name="arrow-back"
            size={SIZES.iconMd}
            color={COLORS.text}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detalhes do Serviço</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.imageContainer}>
          {service.image ? (
            <Image
              source={{ uri: service.image }}
              style={styles.image}
              contentFit="cover"
            />
          ) : (
            <View style={[styles.imagePlaceholder, { backgroundColor: service.category.color }]}>
              <Ionicons
                name={service.category.icon as any}
                size={SIZES.iconXl}
                color={COLORS.textLight}
              />
            </View>
          )}
        </View>

        <View style={styles.serviceInfo}>
          <View style={styles.titleSection}>
            <Text style={styles.serviceName}>{service.name}</Text>
            <Text style={styles.servicePrice}>{formatPrice(service.price)}</Text>
          </View>

          <View style={styles.categorySection}>
            <View
              style={[
                styles.categoryDot,
                { backgroundColor: service.category.color }
              ]}
            />
            <Text style={styles.categoryName}>{service.category.name}</Text>
          </View>

          <View style={styles.detailsSection}>
            <View style={styles.detailItem}>
              <Ionicons
                name="time-outline"
                size={SIZES.iconSm}
                color={COLORS.primary}
              />
              <Text style={styles.detailText}>
                Duração: {formatDuration(service.duration)}
              </Text>
            </View>

            <View style={styles.detailItem}>
              <Ionicons
                name="checkmark-circle-outline"
                size={SIZES.iconSm}
                color={COLORS.success}
              />
              <Text style={styles.detailText}>
                Serviço ativo
              </Text>
            </View>
          </View>

          <View style={styles.descriptionSection}>
            <Text style={styles.descriptionTitle}>Descrição</Text>
            <Text style={styles.descriptionText}>{service.description}</Text>
          </View>

          <View style={styles.featuresSection}>
            <Text style={styles.featuresTitle}>O que está incluso:</Text>
            <View style={styles.featuresList}>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark"
                  size={SIZES.iconXs}
                  color={COLORS.success}
                />
                <Text style={styles.featureText}>Atendimento personalizado</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark"
                  size={SIZES.iconXs}
                  color={COLORS.success}
                />
                <Text style={styles.featureText}>Produtos de alta qualidade</Text>
              </View>
              <View style={styles.featureItem}>
                <Ionicons
                  name="checkmark"
                  size={SIZES.iconXs}
                  color={COLORS.success}
                />
                <Text style={styles.featureText}>Profissionais especializados</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.priceInfo}>
          <Text style={styles.priceLabel}>Total</Text>
          <Text style={styles.totalPrice}>{formatPrice(service.price)}</Text>
        </View>
        <Button
          title="Agendar Serviço"
          onPress={() => onBookService(service)}
          style={styles.bookButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.lg,
    paddingVertical: SIZES.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: SIZES.sm,
  },
  headerTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  headerSpacer: {
    width: SIZES.iconMd + SIZES.sm * 2,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    height: 250,
    backgroundColor: COLORS.backgroundDark,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  serviceInfo: {
    padding: SIZES.lg,
  },
  titleSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.md,
  },
  serviceName: {
    flex: 1,
    fontSize: SIZES.fontXxl,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginRight: SIZES.md,
  },
  servicePrice: {
    fontSize: SIZES.fontXxl,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  categorySection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.lg,
  },
  categoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: SIZES.sm,
  },
  categoryName: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
  },
  detailsSection: {
    marginBottom: SIZES.lg,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.sm,
  },
  detailText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.text,
    marginLeft: SIZES.sm,
  },
  descriptionSection: {
    marginBottom: SIZES.lg,
  },
  descriptionTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.sm,
  },
  descriptionText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    lineHeight: 24,
  },
  featuresSection: {
    marginBottom: SIZES.lg,
  },
  featuresTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.md,
  },
  featuresList: {
    // Container para a lista de features
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.sm,
  },
  featureText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.text,
    marginLeft: SIZES.sm,
  },
  footer: {
    padding: SIZES.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    backgroundColor: COLORS.surface,
  },
  priceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.md,
  },
  priceLabel: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.medium,
    color: COLORS.text,
  },
  totalPrice: {
    fontSize: SIZES.fontXxl,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  bookButton: {
    // Estilo do botão já definido no componente Button
  },
});