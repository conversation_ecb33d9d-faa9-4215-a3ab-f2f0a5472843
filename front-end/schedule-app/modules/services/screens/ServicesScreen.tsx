import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Input } from '../../../components/common';
import { COLORS, FONTS, SIZES } from '../../../constants/AppConstants';
import { mockServiceCategories, mockServices } from '../../../data/mockData';
import { Service, ServiceCategory } from '../../../types';

const { width } = Dimensions.get('window');

interface ServicesScreenProps {
  onServicePress: (service: Service) => void;
}

export const ServicesScreen: React.FC<ServicesScreenProps> = ({
  onServicePress,
}) => {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadServices();
  }, []);

  const loadServices = async () => {
    try {
      setLoading(true);
      // Simulação de carregamento
      await new Promise(resolve => setTimeout(resolve, 1000));

      setServices(mockServices);
      setCategories(mockServiceCategories);
    } catch (error) {
      console.error('Erro ao carregar serviços:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredServices = services.filter(service => {
    const matchesCategory = !selectedCategory || service.category.id === selectedCategory;
    const matchesSearch = !searchQuery ||
      service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesSearch && service.isActive;
  });

  const renderCategoryFilter = () => (
    <View style={styles.categoriesContainer}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={[{ id: 'all', name: 'Todos', icon: 'grid-outline', color: COLORS.primary }, ...categories]}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              (selectedCategory === item.id || (item.id === 'all' && !selectedCategory)) &&
              styles.categoryButtonActive
            ]}
            onPress={() => setSelectedCategory(item.id === 'all' ? null : item.id)}
          >
            <View
              style={[
                styles.categoryIcon,
                { backgroundColor: item.color },
                (selectedCategory === item.id || (item.id === 'all' && !selectedCategory)) &&
                styles.categoryIconActive
              ]}
            >
              <Ionicons
                name={item.icon as any}
                size={SIZES.iconSm}
                color={COLORS.textLight}
              />
            </View>
            <Text
              style={[
                styles.categoryText,
                (selectedCategory === item.id || (item.id === 'all' && !selectedCategory)) &&
                styles.categoryTextActive
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.categoriesList}
      />
    </View>
  );

  const renderServiceItem = ({ item }: { item: Service }) => (
    <TouchableOpacity
      style={styles.serviceItem}
      onPress={() => onServicePress(item)}
    >
      <View style={styles.serviceImageContainer}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.serviceImage} />
        ) : (
          <View style={styles.servicePlaceholder}>
            <Ionicons name="cut" size={24} color={COLORS.primary} />
          </View>
        )}
      </View>
      <View style={styles.serviceInfo}>
        <Text style={styles.serviceName} numberOfLines={2}>{item.name}</Text>
        <Text style={styles.servicePrice}>R$ {item.price.toFixed(2)}</Text>
        <Text style={styles.serviceDuration}>{item.duration}min</Text>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons
        name="search-outline"
        size={SIZES.iconXl}
        color={COLORS.textSecondary}
      />
      <Text style={styles.emptyStateText}>
        Nenhum serviço encontrado
      </Text>
      <Text style={styles.emptyStateSubtext}>
        Tente ajustar os filtros ou busca
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Carregando serviços...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Nossos Serviços</Text>
        <Text style={styles.subtitle}>
          Escolha o serviço que deseja agendar
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <Input
          placeholder="Buscar serviços..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search-outline"
          style={styles.searchInput}
        />
      </View>

      {renderCategoryFilter()}

      <FlatList
        data={filteredServices}
        renderItem={renderServiceItem}
        keyExtractor={(item) => item.id}
        numColumns={3}
        contentContainerStyle={styles.servicesList}
        columnWrapperStyle={styles.serviceRow}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginTop: SIZES.md,
  },
  header: {
    padding: SIZES.lg,
    paddingBottom: SIZES.md,
  },
  title: {
    fontSize: SIZES.fontTitle,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.xs,
  },
  subtitle: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  searchContainer: {
    paddingHorizontal: SIZES.lg,
    marginBottom: SIZES.md,
  },
  searchInput: {
    marginBottom: 0,
  },
  categoriesContainer: {
    marginBottom: SIZES.md,
  },
  categoriesList: {
    paddingHorizontal: SIZES.lg,
  },
  categoryButton: {
    alignItems: 'center',
    marginRight: SIZES.md,
    paddingVertical: SIZES.sm,
  },
  categoryButtonActive: {
    // Estilo ativo será aplicado aos elementos filhos
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.xs,
  },
  categoryIconActive: {
    transform: [{ scale: 1.1 }],
  },
  categoryText: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  categoryTextActive: {
    color: COLORS.primary,
    fontFamily: FONTS.bold,
  },
  servicesList: {
    paddingHorizontal: SIZES.lg,
    paddingBottom: SIZES.xl,
  },
  serviceRow: {
    justifyContent: 'space-between',
    marginBottom: SIZES.md,
  },
  serviceItem: {
    width: (width - SIZES.lg * 2 - SIZES.sm * 2) / 3,
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.borderRadius,
    padding: SIZES.sm,
    alignItems: 'center',
    shadowColor: COLORS.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceImageContainer: {
    width: '100%',
    height: 60,
    marginBottom: SIZES.xs,
  },
  serviceImage: {
    width: '100%',
    height: '100%',
    borderRadius: SIZES.borderRadius,
  },
  servicePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: SIZES.borderRadius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  serviceInfo: {
    alignItems: 'center',
    width: '100%',
  },
  serviceName: {
    fontSize: SIZES.fontSmall,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SIZES.xs,
    minHeight: 32,
  },
  servicePrice: {
    fontSize: SIZES.fontSmall,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
    marginBottom: 2,
  },
  serviceDuration: {
    fontSize: SIZES.fontXSmall,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SIZES.xxl,
  },
  emptyStateText: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
    marginTop: SIZES.md,
    marginBottom: SIZES.xs,
  },
  emptyStateSubtext: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});