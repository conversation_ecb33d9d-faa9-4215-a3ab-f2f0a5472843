import { Ionicons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '../../../components/common';
import { COLORS, FONTS, SIZES } from '../../../constants/AppConstants';
import { apiService } from '../../../services/api';
import { AppointmentDTO, AppointmentStatus } from '../../../types';
// import { useAuth } from '../../auth';

interface AppointmentsScreenProps {
  onCreateAppointment?: () => void;
}

export const AppointmentsScreen: React.FC<AppointmentsScreenProps> = ({
  onCreateAppointment,
}) => {
  // const { user } = useAuth();
  const user = { id: 'mock-user-id', name: 'Mock User' }; // Mock user for testing
  const [appointments, setAppointments] = useState<AppointmentDTO[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<'all' | 'upcoming' | 'past'>('upcoming');

  useEffect(() => {
    loadAppointments();
  }, [filter]);

  const loadAppointments = async () => {
    if (!user) return;

    try {
      setLoading(true);
      let appointmentsData: AppointmentDTO[];
      
      if (filter === 'upcoming') {
        appointmentsData = await apiService.getUpcomingAppointments(user.id);
      } else {
        appointmentsData = await apiService.getUserAppointments(user.id);
        
        if (filter === 'past') {
          const now = new Date();
          appointmentsData = appointmentsData.filter(apt => {
            const appointmentDate = new Date(`${apt.date}T${apt.startTime}`);
            return appointmentDate < now;
          });
        }
      }

      setAppointments(appointmentsData);
    } catch (error) {
      console.error('Erro ao carregar agendamentos:', error);
      Alert.alert('Erro', 'Não foi possível carregar os agendamentos.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadAppointments();
    setRefreshing(false);
  }, [filter]);

  const handleCancelAppointment = (appointment: AppointmentDTO) => {
    Alert.alert(
      'Cancelar Agendamento',
      `Tem certeza que deseja cancelar o agendamento de ${appointment.service.name} para ${formatDate(appointment.date)} às ${appointment.startTime}?`,
      [
        { text: 'Não', style: 'cancel' },
        {
          text: 'Sim, cancelar',
          style: 'destructive',
          onPress: () => confirmCancelAppointment(appointment.id)
        }
      ]
    );
  };

  const confirmCancelAppointment = async (appointmentId: string) => {
    try {
      await apiService.cancelAppointment(appointmentId);
      Alert.alert('Sucesso', 'Agendamento cancelado com sucesso.');
      loadAppointments();
    } catch (error) {
      console.error('Erro ao cancelar agendamento:', error);
      Alert.alert('Erro', 'Não foi possível cancelar o agendamento.');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number) => {
    return `R$ ${price.toFixed(2).replace('.', ',')}`;
  };

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return COLORS.warning;
      case AppointmentStatus.CONFIRMED:
        return COLORS.primary;
      case AppointmentStatus.IN_PROGRESS:
        return COLORS.info;
      case AppointmentStatus.COMPLETED:
        return COLORS.success;
      case AppointmentStatus.CANCELLED:
        return COLORS.error;
      case AppointmentStatus.NO_SHOW:
        return COLORS.textSecondary;
      default:
        return COLORS.textSecondary;
    }
  };

  const getStatusText = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.SCHEDULED:
        return 'Agendado';
      case AppointmentStatus.CONFIRMED:
        return 'Confirmado';
      case AppointmentStatus.IN_PROGRESS:
        return 'Em andamento';
      case AppointmentStatus.COMPLETED:
        return 'Concluído';
      case AppointmentStatus.CANCELLED:
        return 'Cancelado';
      case AppointmentStatus.NO_SHOW:
        return 'Não compareceu';
      default:
        return status;
    }
  };

  const canCancelAppointment = (appointment: AppointmentDTO) => {
    const appointmentDate = new Date(`${appointment.date}T${appointment.startTime}`);
    const now = new Date();
    const hoursDifference = (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    return (
      appointment.status === AppointmentStatus.SCHEDULED ||
      appointment.status === AppointmentStatus.CONFIRMED
    ) && hoursDifference > 2; // Pode cancelar até 2 horas antes
  };

  const renderAppointmentItem = ({ item }: { item: AppointmentDTO }) => (
    <View style={styles.appointmentCard}>
      <View style={styles.appointmentHeader}>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{item.service.name}</Text>
          <Text style={styles.professionalName}>{item.professional.name}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>

      <View style={styles.appointmentDetails}>
        <View style={styles.detailRow}>
          <Ionicons name="calendar-outline" size={16} color={COLORS.textSecondary} />
          <Text style={styles.detailText}>{formatDate(item.date)}</Text>
        </View>
        <View style={styles.detailRow}>
          <Ionicons name="time-outline" size={16} color={COLORS.textSecondary} />
          <Text style={styles.detailText}>{item.startTime} - {item.endTime}</Text>
        </View>
        <View style={styles.detailRow}>
          <Ionicons name="cash-outline" size={16} color={COLORS.textSecondary} />
          <Text style={styles.detailText}>{formatPrice(item.totalPrice)}</Text>
        </View>
        {item.notes && (
          <View style={styles.detailRow}>
            <Ionicons name="document-text-outline" size={16} color={COLORS.textSecondary} />
            <Text style={styles.detailText}>{item.notes}</Text>
          </View>
        )}
      </View>

      {canCancelAppointment(item) && (
        <View style={styles.appointmentActions}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => handleCancelAppointment(item)}
          >
            <Text style={styles.cancelButtonText}>Cancelar</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="calendar-outline" size={64} color={COLORS.textSecondary} />
      <Text style={styles.emptyTitle}>
        {filter === 'upcoming' ? 'Nenhum agendamento próximo' : 'Nenhum agendamento encontrado'}
      </Text>
      <Text style={styles.emptySubtitle}>
        {filter === 'upcoming' 
          ? 'Que tal agendar um novo serviço?' 
          : 'Seus agendamentos aparecerão aqui'
        }
      </Text>
      {onCreateAppointment && (
        <Button
          title="Agendar Serviço"
          onPress={onCreateAppointment}
          style={styles.emptyButton}
        />
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Meus Agendamentos</Text>
      </View>

      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'upcoming' && styles.filterButtonActive]}
          onPress={() => setFilter('upcoming')}
        >
          <Text style={[styles.filterText, filter === 'upcoming' && styles.filterTextActive]}>
            Próximos
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterText, filter === 'all' && styles.filterTextActive]}>
            Todos
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'past' && styles.filterButtonActive]}
          onPress={() => setFilter('past')}
        >
          <Text style={[styles.filterText, filter === 'past' && styles.filterTextActive]}>
            Histórico
          </Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Carregando agendamentos...</Text>
        </View>
      ) : (
        <FlatList
          data={appointments}
          renderItem={renderAppointmentItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SIZES.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: SIZES.fontXl,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: SIZES.lg,
    paddingVertical: SIZES.md,
    backgroundColor: COLORS.surface,
  },
  filterButton: {
    flex: 1,
    paddingVertical: SIZES.sm,
    paddingHorizontal: SIZES.md,
    marginHorizontal: SIZES.xs,
    borderRadius: SIZES.radiusSm,
    backgroundColor: COLORS.backgroundLight,
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: COLORS.primary,
  },
  filterText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
  },
  filterTextActive: {
    color: COLORS.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginTop: SIZES.sm,
  },
  listContainer: {
    padding: SIZES.lg,
  },
  appointmentCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusMd,
    padding: SIZES.lg,
    marginBottom: SIZES.md,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.xs,
  },
  professionalName: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  statusBadge: {
    paddingHorizontal: SIZES.sm,
    paddingVertical: SIZES.xs,
    borderRadius: SIZES.radiusSm,
  },
  statusText: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.medium,
    color: COLORS.white,
  },
  appointmentDetails: {
    marginBottom: SIZES.md,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.xs,
  },
  detailText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.text,
    marginLeft: SIZES.sm,
  },
  appointmentActions: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: SIZES.md,
    alignItems: 'flex-end',
  },
  cancelButton: {
    paddingHorizontal: SIZES.lg,
    paddingVertical: SIZES.sm,
    borderRadius: SIZES.radiusSm,
    borderWidth: 1,
    borderColor: COLORS.error,
  },
  cancelButtonText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.error,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SIZES.xl,
  },
  emptyTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    textAlign: 'center',
    marginTop: SIZES.lg,
    marginBottom: SIZES.sm,
  },
  emptySubtitle: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SIZES.xl,
  },
  emptyButton: {
    marginTop: SIZES.lg,
  },
});

export default AppointmentsScreen;
