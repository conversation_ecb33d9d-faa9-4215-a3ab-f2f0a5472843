import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button, Input } from '../../../components/common';
import { COLORS, FONTS, SIZES } from '../../../constants/AppConstants';
import { apiService } from '../../../services/api';
import { BookingRequestDTO, Professional, Service, TimeSlotDTO } from '../../../types';
// import { useAuth } from '../../auth';

interface BookingScreenProps {
  service: Service;
  onGoBack: () => void;
  onBookingConfirm: (bookingData: any) => void;
}

export const BookingScreen: React.FC<BookingScreenProps> = ({
  service,
  onGoBack,
  onBookingConfirm,
}) => {
  // const { user } = useAuth();
  const user = { id: 'mock-user-id', name: 'Mock User' }; // Mock user for testing
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [selectedProfessional, setSelectedProfessional] = useState<Professional | null>(null);
  const [availableSlots, setAvailableSlots] = useState<TimeSlotDTO[]>([]);
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingProfessionals, setLoadingProfessionals] = useState(true);
  const [loadingSlots, setLoadingSlots] = useState(false);

  // Gerar próximos 14 dias
  const generateDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  const availableDates = generateDates();

  // Carregar profissionais ao montar o componente
  useEffect(() => {
    loadProfessionals();
  }, []);

  // Carregar horários disponíveis quando profissional e data são selecionados
  useEffect(() => {
    if (selectedProfessional && selectedDate) {
      loadAvailableSlots();
    } else {
      setAvailableSlots([]);
    }
  }, [selectedDate, selectedProfessional]);

  const loadProfessionals = async () => {
    try {
      setLoadingProfessionals(true);
      const professionalsData = await apiService.getProfessionalsByService(service.id);
      setProfessionals(professionalsData);
    } catch (error) {
      console.error('Erro ao carregar profissionais:', error);
      Alert.alert('Erro', 'Não foi possível carregar os profissionais. Tente novamente.');
    } finally {
      setLoadingProfessionals(false);
    }
  };

  const loadAvailableSlots = async () => {
    if (!selectedProfessional || !selectedDate) return;

    try {
      setLoadingSlots(true);
      const dateString = selectedDate.toISOString().split('T')[0]; // YYYY-MM-DD
      const slots = await apiService.getAvailableTimeSlots(selectedProfessional.id, dateString);
      setAvailableSlots(slots);
    } catch (error) {
      console.error('Erro ao carregar horários:', error);
      Alert.alert('Erro', 'Não foi possível carregar os horários disponíveis. Tente novamente.');
      setAvailableSlots([]);
    } finally {
      setLoadingSlots(false);
    }
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Hoje';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Amanhã';
    } else {
      return date.toLocaleDateString('pt-BR', {
        weekday: 'short',
        day: '2-digit',
        month: '2-digit',
      });
    }
  };

  const formatPrice = (price: number) => {
    return `R$ ${price.toFixed(2).replace('.', ',')}`;
  };

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;

    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${minutes}min`;
    }
  };

  const handleBooking = () => {
    if (!selectedDate || !selectedTime || !selectedProfessional) {
      Alert.alert('Atenção', 'Por favor, selecione data, horário e profissional.');
      return;
    }

    if (!user) {
      Alert.alert('Erro', 'Usuário não autenticado.');
      return;
    }

    Alert.alert(
      'Confirmar Agendamento',
      `Serviço: ${service.name}\nProfissional: ${selectedProfessional.name}\nData: ${selectedDate.toLocaleDateString('pt-BR')}\nHorário: ${selectedTime}\nValor: ${formatPrice(service.price)}`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Confirmar',
          onPress: () => confirmBooking()
        }
      ]
    );
  };

  const confirmBooking = async () => {
    if (!selectedDate || !selectedTime || !selectedProfessional || !user) return;

    try {
      setLoading(true);

      const bookingRequest: BookingRequestDTO = {
        userId: user.id,
        serviceId: service.id,
        professionalId: selectedProfessional.id,
        date: selectedDate.toISOString().split('T')[0], // YYYY-MM-DD
        time: selectedTime,
        notes: notes || undefined,
      };

      const createdAppointment = await apiService.createAppointment(bookingRequest);

      const bookingData = {
        appointment: createdAppointment,
        service,
        professional: selectedProfessional,
        date: selectedDate,
        time: selectedTime,
        notes,
        totalPrice: service.price,
      };

      onBookingConfirm(bookingData);
    } catch (error) {
      console.error('Erro ao criar agendamento:', error);
      Alert.alert('Erro', 'Não foi possível criar o agendamento. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={onGoBack}>
          <Ionicons name="arrow-back" size={SIZES.iconMd} color={COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Agendar Serviço</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Informações do serviço */}
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceName}>{service.name}</Text>
          <View style={styles.serviceDetails}>
            <Text style={styles.servicePrice}>{formatPrice(service.price)}</Text>
            <Text style={styles.serviceDuration}>{formatDuration(service.duration)}</Text>
          </View>
        </View>

        {/* Seleção de profissional */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Escolha o Profissional</Text>
          {loadingProfessionals ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={COLORS.primary} />
              <Text style={styles.loadingText}>Carregando profissionais...</Text>
            </View>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {professionals.map((professional) => (
                <TouchableOpacity
                  key={professional.id}
                  style={[
                    styles.professionalCard,
                    selectedProfessional?.id === professional.id && styles.professionalCardSelected
                  ]}
                  onPress={() => setSelectedProfessional(professional)}
                >
                  <View style={styles.professionalAvatar}>
                    <Ionicons name="person" size={SIZES.iconMd} color={COLORS.textLight} />
                  </View>
                  <Text style={styles.professionalName}>{professional.name}</Text>
                  <Text style={styles.professionalSpecialty}>
                    {professional.specialties.join(', ')}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Seleção de data */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Escolha a Data</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {availableDates.map((date, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dateCard,
                  selectedDate?.toDateString() === date.toDateString() && styles.dateCardSelected
                ]}
                onPress={() => setSelectedDate(date)}
              >
                <Text style={[
                  styles.dateText,
                  selectedDate?.toDateString() === date.toDateString() && styles.dateTextSelected
                ]}>
                  {formatDate(date)}
                </Text>
                <Text style={[
                  styles.dayText,
                  selectedDate?.toDateString() === date.toDateString() && styles.dayTextSelected
                ]}>
                  {date.getDate()}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Seleção de horário */}
        {selectedDate && selectedProfessional && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Escolha o Horário</Text>
            {loadingSlots ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={COLORS.primary} />
                <Text style={styles.loadingText}>Carregando horários...</Text>
              </View>
            ) : availableSlots.length > 0 ? (
              <View style={styles.timeGrid}>
                {availableSlots.map((slot) => (
                  <TouchableOpacity
                    key={slot.time}
                    style={[
                      styles.timeSlot,
                      !slot.available && styles.timeSlotDisabled,
                      selectedTime === slot.time && styles.timeSlotSelected
                    ]}
                    onPress={() => slot.available && setSelectedTime(slot.time)}
                    disabled={!slot.available}
                  >
                    <Text style={[
                      styles.timeText,
                      !slot.available && styles.timeTextDisabled,
                      selectedTime === slot.time && styles.timeTextSelected
                    ]}>
                      {slot.time}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <View style={styles.noSlotsContainer}>
                <Text style={styles.noSlotsText}>
                  Nenhum horário disponível para esta data
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Observações */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Observações (Opcional)</Text>
          <Input
            placeholder="Alguma observação especial para o atendimento?"
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={3}
            style={styles.notesInput}
          />
        </View>

        {/* Resumo */}
        {selectedDate && selectedTime && selectedProfessional && (
          <View style={styles.summarySection}>
            <Text style={styles.sectionTitle}>Resumo do Agendamento</Text>
            <View style={styles.summaryCard}>
              <View style={styles.summaryItem}>
                <Ionicons name="cut" size={SIZES.iconSm} color={COLORS.primary} />
                <Text style={styles.summaryLabel}>Serviço:</Text>
                <Text style={styles.summaryValue}>{service.name}</Text>
              </View>

              <View style={styles.summaryItem}>
                <Ionicons name="person" size={SIZES.iconSm} color={COLORS.primary} />
                <Text style={styles.summaryLabel}>Profissional:</Text>
                <Text style={styles.summaryValue}>{selectedProfessional.name}</Text>
              </View>

              <View style={styles.summaryItem}>
                <Ionicons name="calendar" size={SIZES.iconSm} color={COLORS.primary} />
                <Text style={styles.summaryLabel}>Data:</Text>
                <Text style={styles.summaryValue}>
                  {selectedDate.toLocaleDateString('pt-BR')}
                </Text>
              </View>

              <View style={styles.summaryItem}>
                <Ionicons name="time" size={SIZES.iconSm} color={COLORS.primary} />
                <Text style={styles.summaryLabel}>Horário:</Text>
                <Text style={styles.summaryValue}>{selectedTime}</Text>
              </View>

              <View style={styles.summaryItem}>
                <Ionicons name="card" size={SIZES.iconSm} color={COLORS.primary} />
                <Text style={styles.summaryLabel}>Valor:</Text>
                <Text style={styles.summaryPrice}>{formatPrice(service.price)}</Text>
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Footer com botão de confirmação */}
      <View style={styles.footer}>
        <Button
          title="Confirmar Agendamento"
          onPress={handleBooking}
          disabled={!selectedDate || !selectedTime || !selectedProfessional}
          loading={loading}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SIZES.lg,
    paddingVertical: SIZES.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  backButton: {
    padding: SIZES.sm,
  },
  headerTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  headerSpacer: {
    width: SIZES.iconMd + SIZES.sm * 2,
  },
  content: {
    flex: 1,
  },
  serviceInfo: {
    padding: SIZES.lg,
    backgroundColor: COLORS.primaryLight,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  serviceName: {
    fontSize: SIZES.fontXl,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.sm,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  servicePrice: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  serviceDuration: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
  },
  section: {
    padding: SIZES.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  sectionTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.md,
  },
  professionalCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.md,
    marginRight: SIZES.md,
    alignItems: 'center',
    minWidth: 120,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  professionalCardSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primaryLight,
  },
  professionalAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SIZES.sm,
  },
  professionalName: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SIZES.xs,
  },
  professionalSpecialty: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  dateCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.md,
    marginRight: SIZES.md,
    alignItems: 'center',
    minWidth: 80,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  dateCardSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primaryLight,
  },
  dateText: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
    marginBottom: SIZES.xs,
  },
  dateTextSelected: {
    color: COLORS.primary,
  },
  dayText: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  dayTextSelected: {
    color: COLORS.primary,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.sm,
  },
  timeSlot: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusMd,
    paddingVertical: SIZES.md,
    paddingHorizontal: SIZES.lg,
    borderWidth: 1,
    borderColor: COLORS.border,
    minWidth: 80,
    alignItems: 'center',
  },
  timeSlotSelected: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  timeSlotDisabled: {
    backgroundColor: COLORS.backgroundDark,
    opacity: 0.5,
  },
  timeText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.medium,
    color: COLORS.text,
  },
  timeTextSelected: {
    color: COLORS.textLight,
  },
  timeTextDisabled: {
    color: COLORS.textSecondary,
  },
  notesInput: {
    marginBottom: 0,
  },
  summarySection: {
    padding: SIZES.lg,
    paddingBottom: SIZES.xxl,
  },
  summaryCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.lg,
    shadowColor: COLORS.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.md,
  },
  summaryLabel: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
    marginLeft: SIZES.sm,
    flex: 1,
  },
  summaryValue: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  summaryPrice: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  footer: {
    padding: SIZES.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    backgroundColor: COLORS.surface,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.lg,
  },
  loadingText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginTop: SIZES.sm,
  },
  noSlotsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SIZES.lg,
    backgroundColor: COLORS.backgroundLight,
    borderRadius: SIZES.radiusMd,
  },
  noSlotsText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
});

export default BookingScreen;