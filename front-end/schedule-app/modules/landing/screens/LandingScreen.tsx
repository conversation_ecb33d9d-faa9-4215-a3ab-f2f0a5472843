import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React from 'react';
import {
    Dimensions,
    FlatList,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '../../../components/common';
import { COLORS, FONTS, SALON_INFO, SIZES } from '../../../constants/AppConstants';
import { mockServices } from '../../../data/mockData';
import { Service } from '../../../types';

const { width, height } = Dimensions.get('window');

interface LandingScreenProps {
  onServiceSelect: (service: Service) => void;
  onViewAllServices: () => void;
}

export const LandingScreen: React.FC<LandingScreenProps> = ({
  onServiceSelect,
  onViewAllServices,
}) => {
  // Serviços em destaque (primeiros 6 para mostrar 2 linhas de 3)
  const featuredServices = mockServices.slice(0, 6);

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.logoContainer}>
        <View style={styles.iconContainer}>
          <Ionicons name="cut" size={40} color={COLORS.textLight} />
        </View>
        <View style={styles.headerText}>
          <Text style={styles.salonName}>{SALON_INFO.name}</Text>
          <Text style={styles.tagline}>{SALON_INFO.tagline}</Text>
        </View>
      </View>
    </View>
  );

  const renderWelcomeSection = () => (
    <View style={styles.welcomeSection}>
      <Text style={styles.welcomeTitle}>Bem-vindo ao seu salão de beleza!</Text>
      <Text style={styles.welcomeSubtitle}>
        Agende seus serviços de forma rápida e prática
      </Text>
    </View>
  );

  const renderServiceItem = ({ item }: { item: Service }) => (
    <TouchableOpacity 
      style={styles.serviceItem}
      onPress={() => onServiceSelect(item)}
    >
      <View style={styles.serviceImageContainer}>
        {item.image ? (
          <Image source={{ uri: item.image }} style={styles.serviceImage} />
        ) : (
          <View style={styles.servicePlaceholder}>
            <Ionicons name="cut" size={24} color={COLORS.primary} />
          </View>
        )}
      </View>
      <View style={styles.serviceInfo}>
        <Text style={styles.serviceName} numberOfLines={2}>{item.name}</Text>
        <Text style={styles.servicePrice}>R$ {item.price.toFixed(2)}</Text>
        <Text style={styles.serviceDuration}>{item.duration}min</Text>
      </View>
    </TouchableOpacity>
  );

  const renderFeaturedServices = () => (
    <View style={styles.featuredSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Serviços em Destaque</Text>
        <TouchableOpacity onPress={onViewAllServices} style={styles.viewAllButton}>
          <Text style={styles.viewAllText}>Ver todos</Text>
          <Ionicons name="arrow-forward" size={16} color={COLORS.primary} />
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={featuredServices}
        renderItem={renderServiceItem}
        keyExtractor={(item) => item.id}
        numColumns={3}
        contentContainerStyle={styles.servicesGrid}
        columnWrapperStyle={styles.serviceRow}
        scrollEnabled={false}
      />
    </View>
  );

  const renderSalonInfo = () => (
    <View style={styles.infoSection}>
      <Text style={styles.sectionTitle}>Sobre o Salão</Text>
      <Text style={styles.infoDescription}>{SALON_INFO.description}</Text>
      
      <View style={styles.contactInfo}>
        <View style={styles.contactItem}>
          <Ionicons name="location" size={20} color={COLORS.primary} />
          <Text style={styles.contactText}>{SALON_INFO.address.full}</Text>
        </View>
        
        <View style={styles.contactItem}>
          <Ionicons name="call" size={20} color={COLORS.primary} />
          <Text style={styles.contactText}>{SALON_INFO.contact.phone}</Text>
        </View>
        
        <View style={styles.contactItem}>
          <Ionicons name="time" size={20} color={COLORS.primary} />
          <Text style={styles.contactText}>{SALON_INFO.hours.weekdays}</Text>
        </View>
      </View>
    </View>
  );

  const renderFeatures = () => (
    <View style={styles.featuresSection}>
      <Text style={styles.sectionTitle}>Por que nos escolher?</Text>
      <View style={styles.featuresGrid}>
        {SALON_INFO.features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Ionicons name="checkmark-circle" size={20} color={COLORS.success} />
            <Text style={styles.featureText}>{feature}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderCTA = () => (
    <View style={styles.ctaSection}>
      <Text style={styles.ctaTitle}>Pronto para se cuidar?</Text>
      <Text style={styles.ctaSubtitle}>
        Escolha um de nossos serviços e agende seu horário
      </Text>
      <Button
        title="Ver Todos os Serviços"
        onPress={onViewAllServices}
        style={styles.ctaButton}
      />
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {renderHeader()}
        {renderWelcomeSection()}
        {renderFeaturedServices()}
        {renderSalonInfo()}
        {renderFeatures()}
        {renderCTA()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SIZES.xl,
  },
  header: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: SIZES.lg,
    paddingVertical: SIZES.xl,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.primaryDark,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SIZES.md,
  },
  headerText: {
    flex: 1,
  },
  salonName: {
    fontSize: SIZES.fontTitle,
    fontFamily: FONTS.bold,
    color: COLORS.textLight,
    marginBottom: SIZES.xs,
  },
  tagline: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.regular,
    color: COLORS.textLight,
    opacity: 0.9,
  },
  welcomeSection: {
    padding: SIZES.lg,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: SIZES.fontLarge,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SIZES.sm,
  },
  welcomeSubtitle: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  featuredSection: {
    paddingVertical: SIZES.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SIZES.lg,
    marginBottom: SIZES.md,
  },
  sectionTitle: {
    fontSize: SIZES.fontLarge,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
    marginRight: SIZES.xs,
  },
  servicesGrid: {
    paddingHorizontal: SIZES.lg,
  },
  serviceRow: {
    justifyContent: 'space-between',
    marginBottom: SIZES.md,
  },
  serviceItem: {
    width: (width - SIZES.lg * 2 - SIZES.sm * 2) / 3,
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.borderRadius,
    padding: SIZES.sm,
    alignItems: 'center',
    shadowColor: COLORS.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceImageContainer: {
    width: '100%',
    height: 60,
    marginBottom: SIZES.xs,
  },
  serviceImage: {
    width: '100%',
    height: '100%',
    borderRadius: SIZES.borderRadius,
  },
  servicePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: SIZES.borderRadius,
    alignItems: 'center',
    justifyContent: 'center',
  },
  serviceInfo: {
    alignItems: 'center',
    width: '100%',
  },
  serviceName: {
    fontSize: SIZES.fontSmall,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SIZES.xs,
    minHeight: 32,
  },
  servicePrice: {
    fontSize: SIZES.fontSmall,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
    marginBottom: 2,
  },
  serviceDuration: {
    fontSize: SIZES.fontXSmall,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  infoSection: {
    padding: SIZES.lg,
    backgroundColor: COLORS.surface,
    marginHorizontal: SIZES.lg,
    borderRadius: SIZES.borderRadius,
    marginBottom: SIZES.lg,
  },
  infoDescription: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    lineHeight: 22,
    marginBottom: SIZES.lg,
  },
  contactInfo: {
    gap: SIZES.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  contactText: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.regular,
    color: COLORS.text,
    marginLeft: SIZES.sm,
    flex: 1,
  },
  featuresSection: {
    padding: SIZES.lg,
  },
  featuresGrid: {
    gap: SIZES.sm,
    marginTop: SIZES.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureText: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.regular,
    color: COLORS.text,
    marginLeft: SIZES.sm,
  },
  ctaSection: {
    padding: SIZES.lg,
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    marginHorizontal: SIZES.lg,
    borderRadius: SIZES.borderRadius,
  },
  ctaTitle: {
    fontSize: SIZES.fontLarge,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SIZES.sm,
  },
  ctaSubtitle: {
    fontSize: SIZES.fontMedium,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SIZES.lg,
    lineHeight: 22,
  },
  ctaButton: {
    minWidth: 200,
  },
});
