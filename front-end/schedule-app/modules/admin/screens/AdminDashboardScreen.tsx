import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { AdminDashboard, Appointment, AppointmentStatus } from '../../../types';
import { mockAdminDashboard } from '../../../data/mockData';
import { COLORS, SIZES, FONTS } from '../../../constants/AppConstants';

const { width } = Dimensions.get('window');

interface AdminDashboardScreenProps {
  onLogout: () => void;
}

export const AdminDashboardScreen: React.FC<AdminDashboardScreenProps> = ({
  onLogout,
}) => {
  const [dashboardData, setDashboardData] = useState<AdminDashboard>(mockAdminDashboard);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, [selectedPeriod]);

  const loadDashboardData = async () => {
    setLoading(true);
    // Simulação de carregamento
    await new Promise(resolve => setTimeout(resolve, 1000));
    setDashboardData(mockAdminDashboard);
    setLoading(false);
  };

  const formatCurrency = (value: number) => {
    return `R$ ${value.toFixed(2).replace('.', ',')}`;
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.COMPLETED:
        return COLORS.success;
      case AppointmentStatus.CONFIRMED:
        return COLORS.primary;
      case AppointmentStatus.SCHEDULED:
        return COLORS.warning;
      case AppointmentStatus.CANCELLED:
        return COLORS.error;
      default:
        return COLORS.textSecondary;
    }
  };

  const getStatusText = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.COMPLETED:
        return 'Concluído';
      case AppointmentStatus.CONFIRMED:
        return 'Confirmado';
      case AppointmentStatus.SCHEDULED:
        return 'Agendado';
      case AppointmentStatus.CANCELLED:
        return 'Cancelado';
      default:
        return status;
    }
  };

  const getCurrentStats = () => {
    switch (selectedPeriod) {
      case 'today':
        return dashboardData.todayStats;
      case 'week':
        return dashboardData.weeklyStats;
      case 'month':
        return dashboardData.monthlyStats;
      default:
        return dashboardData.todayStats;
    }
  };

  const renderStatCard = (title: string, value: string | number, icon: string, color: string) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <Ionicons name={icon as any} size={SIZES.iconMd} color={color} />
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <Text style={styles.statValue}>{value}</Text>
    </View>
  );

  const renderAppointmentItem = ({ item }: { item: Appointment }) => (
    <View style={styles.appointmentItem}>
      <View style={styles.appointmentHeader}>
        <Text style={styles.appointmentTime}>
          {item.startTime} - {item.endTime}
        </Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      <Text style={styles.appointmentService}>Serviço ID: {item.serviceId}</Text>
      <Text style={styles.appointmentPrice}>{formatCurrency(item.totalPrice)}</Text>
    </View>
  );

  const renderServiceReport = ({ item }: { item: any }) => (
    <View style={styles.reportItem}>
      <Text style={styles.reportTitle}>{item.service.name}</Text>
      <View style={styles.reportStats}>
        <Text style={styles.reportStat}>{item.totalBookings} agendamentos</Text>
        <Text style={styles.reportRevenue}>{formatCurrency(item.totalRevenue)}</Text>
      </View>
    </View>
  );

  const renderProfessionalReport = ({ item }: { item: any }) => (
    <View style={styles.reportItem}>
      <Text style={styles.reportTitle}>{item.professional.name}</Text>
      <View style={styles.reportStats}>
        <Text style={styles.reportStat}>{item.totalAppointments} atendimentos</Text>
        <Text style={styles.reportStat}>{formatPercentage(item.completionRate)} conclusão</Text>
        <Text style={styles.reportRevenue}>{formatCurrency(item.totalRevenue)}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Painel Administrativo</Text>
          <Text style={styles.headerSubtitle}>Visão geral do negócio</Text>
        </View>
        <TouchableOpacity style={styles.logoutButton} onPress={onLogout}>
          <Ionicons name="log-out-outline" size={SIZES.iconMd} color={COLORS.error} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Seletor de período */}
        <View style={styles.periodSelector}>
          {(['today', 'week', 'month'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && styles.periodButtonActive
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text style={[
                styles.periodButtonText,
                selectedPeriod === period && styles.periodButtonTextActive
              ]}>
                {period === 'today' ? 'Hoje' : period === 'week' ? 'Semana' : 'Mês'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Cards de estatísticas */}
        <View style={styles.statsGrid}>
          {renderStatCard(
            'Agendamentos',
            getCurrentStats().appointments,
            'calendar',
            COLORS.primary
          )}
          {renderStatCard(
            'Faturamento',
            formatCurrency(getCurrentStats().revenue),
            'card',
            COLORS.success
          )}
          {selectedPeriod === 'today' && (
            <>
              {renderStatCard(
                'Novos Clientes',
                dashboardData.todayStats.newClients,
                'person-add',
                COLORS.info
              )}
              {renderStatCard(
                'Taxa de Conclusão',
                formatPercentage(dashboardData.todayStats.completionRate),
                'checkmark-circle',
                COLORS.warning
              )}
            </>
          )}
          {selectedPeriod !== 'today' && (
            renderStatCard(
              'Crescimento',
              `+${formatPercentage(getCurrentStats().growth)}`,
              'trending-up',
              COLORS.success
            )
          )}
        </View>

        {/* Agendamentos recentes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Agendamentos Recentes</Text>
          <FlatList
            data={dashboardData.recentAppointments}
            renderItem={renderAppointmentItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>

        {/* Serviços mais populares */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Serviços Mais Populares</Text>
          <FlatList
            data={dashboardData.topServices}
            renderItem={renderServiceReport}
            keyExtractor={(item) => item.service.id}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>

        {/* Profissionais destaque */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profissionais Destaque</Text>
          <FlatList
            data={dashboardData.topProfessionals}
            renderItem={renderProfessionalReport}
            keyExtractor={(item) => item.professional.id}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>

        {/* Ações rápidas */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ações Rápidas</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="calendar" size={SIZES.iconMd} color={COLORS.primary} />
              <Text style={styles.actionText}>Ver Agenda</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="stats-chart" size={SIZES.iconMd} color={COLORS.secondary} />
              <Text style={styles.actionText}>Relatórios</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="people" size={SIZES.iconMd} color={COLORS.info} />
              <Text style={styles.actionText}>Clientes</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="settings" size={SIZES.iconMd} color={COLORS.warning} />
              <Text style={styles.actionText}>Configurações</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.lg,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    backgroundColor: COLORS.surface,
  },
  headerTitle: {
    fontSize: SIZES.fontXl,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  headerSubtitle: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginTop: SIZES.xs,
  },
  logoutButton: {
    padding: SIZES.sm,
  },
  content: {
    flex: 1,
  },
  periodSelector: {
    flexDirection: 'row',
    margin: SIZES.lg,
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.xs,
  },
  periodButton: {
    flex: 1,
    paddingVertical: SIZES.sm,
    paddingHorizontal: SIZES.md,
    borderRadius: SIZES.radiusMd,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: COLORS.primary,
  },
  periodButtonText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
  },
  periodButtonTextActive: {
    color: COLORS.textLight,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SIZES.lg,
    gap: SIZES.md,
  },
  statCard: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.lg,
    width: (width - SIZES.lg * 2 - SIZES.md) / 2,
    borderLeftWidth: 4,
    shadowColor: COLORS.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SIZES.sm,
  },
  statTitle: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
    marginLeft: SIZES.sm,
  },
  statValue: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  section: {
    margin: SIZES.lg,
    marginTop: SIZES.xl,
  },
  sectionTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.md,
  },
  appointmentItem: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusMd,
    padding: SIZES.md,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.sm,
  },
  appointmentTime: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  statusBadge: {
    paddingHorizontal: SIZES.sm,
    paddingVertical: SIZES.xs,
    borderRadius: SIZES.radiusXs,
  },
  statusText: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.medium,
    color: COLORS.textLight,
  },
  appointmentService: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginBottom: SIZES.xs,
  },
  appointmentPrice: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  reportItem: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusMd,
    padding: SIZES.md,
  },
  reportTitle: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.sm,
  },
  reportStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reportStat: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  reportRevenue: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.bold,
    color: COLORS.success,
  },
  separator: {
    height: SIZES.sm,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.md,
  },
  actionButton: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.lg,
    alignItems: 'center',
    width: (width - SIZES.lg * 2 - SIZES.md) / 2,
    shadowColor: COLORS.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    marginTop: SIZES.sm,
    textAlign: 'center',
  },
});