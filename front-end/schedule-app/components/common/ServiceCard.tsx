import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { Service } from '../../types';
import { COLORS, SIZES, FONTS } from '../../constants/AppConstants';

interface ServiceCardProps {
  service: Service;
  onPress: (service: Service) => void;
  style?: ViewStyle;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  onPress,
  style,
}) => {
  const formatPrice = (price: number) => {
    return `R$ ${price.toFixed(2).replace('.', ',')}`;
  };

  const formatDuration = (duration: number) => {
    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;

    if (hours > 0 && minutes > 0) {
      return `${hours}h ${minutes}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${minutes}min`;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={() => onPress(service)}
      activeOpacity={0.8}
    >
      <View style={styles.imageContainer}>
        {service.image ? (
          <Image
            source={{ uri: service.image }}
            style={styles.image}
            contentFit="cover"
          />
        ) : (
          <View style={[styles.imagePlaceholder, { backgroundColor: service.category.color }]}>
            <Ionicons
              name={service.category.icon as any}
              size={SIZES.iconLg}
              color={COLORS.textLight}
            />
          </View>
        )}
      </View>

      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={2}>
            {service.name}
          </Text>
          <Text style={styles.price}>{formatPrice(service.price)}</Text>
        </View>

        <Text style={styles.description} numberOfLines={2}>
          {service.description}
        </Text>

        <View style={styles.footer}>
          <View style={styles.categoryContainer}>
            <View
              style={[
                styles.categoryDot,
                { backgroundColor: service.category.color }
              ]}
            />
            <Text style={styles.categoryText}>{service.category.name}</Text>
          </View>

          <View style={styles.durationContainer}>
            <Ionicons
              name="time-outline"
              size={SIZES.iconXs}
              color={COLORS.textSecondary}
            />
            <Text style={styles.durationText}>
              {formatDuration(service.duration)}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    marginBottom: SIZES.md,
    shadowColor: COLORS.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    height: 120,
    borderTopLeftRadius: SIZES.radiusLg,
    borderTopRightRadius: SIZES.radiusLg,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    padding: SIZES.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SIZES.sm,
  },
  title: {
    flex: 1,
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginRight: SIZES.sm,
  },
  price: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.primary,
  },
  description: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: SIZES.md,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: SIZES.xs,
  },
  categoryText: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  durationText: {
    fontSize: SIZES.fontXs,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
    marginLeft: SIZES.xs,
  },
});