import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SIZES, FONTS, SALON_INFO } from '../../constants/AppConstants';

const { width, height } = Dimensions.get('window');

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  onLoadingComplete,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.5)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const textFadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    startLoadingAnimation();
  }, []);

  const startLoadingAnimation = () => {
    // Animação do logo aparecendo
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Animação do texto aparecendo
      Animated.timing(textFadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }).start();

      // Animação da barra de progresso
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: false,
      }).start();

      // Animação de rotação contínua do ícone
      Animated.loop(
        Animated.timing(rotateAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        })
      ).start();

      // Finalizar loading após 3 segundos
      setTimeout(() => {
        onLoadingComplete();
      }, 3000);
    });
  };

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Logo animado */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <Animated.View
            style={[
              styles.iconContainer,
              {
                transform: [{ rotate: spin }],
              },
            ]}
          >
            <Ionicons name="cut" size={60} color={COLORS.textLight} />
          </Animated.View>
        </Animated.View>

        {/* Texto do salão */}
        <Animated.View
          style={[
            styles.textContainer,
            {
              opacity: textFadeAnim,
            },
          ]}
        >
          <Text style={styles.salonName}>{SALON_INFO.name}</Text>
          <Text style={styles.tagline}>{SALON_INFO.tagline}</Text>
        </Animated.View>

        {/* Barra de progresso */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  width: progressWidth,
                },
              ]}
            />
          </View>
          <Animated.Text
            style={[
              styles.loadingText,
              {
                opacity: textFadeAnim,
              },
            ]}
          >
            Carregando...
          </Animated.Text>
        </View>
      </View>

      {/* Versão do app */}
      <Animated.View
        style={[
          styles.footer,
          {
            opacity: textFadeAnim,
          },
        ]}
      >
        <Text style={styles.versionText}>Versão 1.0.0</Text>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.primary,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SIZES.xl,
  },
  logoContainer: {
    marginBottom: SIZES.xxl,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.primaryDark,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.text,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: SIZES.xxl,
  },
  salonName: {
    fontSize: SIZES.fontTitle + 8,
    fontFamily: FONTS.bold,
    color: COLORS.textLight,
    textAlign: 'center',
    marginBottom: SIZES.sm,
    letterSpacing: 1,
  },
  tagline: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.regular,
    color: COLORS.primaryLight,
    textAlign: 'center',
    opacity: 0.9,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '80%',
    height: 4,
    backgroundColor: COLORS.primaryLight,
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: SIZES.md,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.textLight,
    borderRadius: 2,
  },
  loadingText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.medium,
    color: COLORS.textLight,
    opacity: 0.8,
  },
  footer: {
    alignItems: 'center',
    paddingBottom: SIZES.xl,
  },
  versionText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.regular,
    color: COLORS.primaryLight,
    opacity: 0.7,
  },
});