import { Service, ServiceCategory, Professional, Appointment, AppointmentStatus, AdminDashboard, DailyReport, MonthlyReport } from '../types';
import { SERVICE_CATEGORIES, COLORS } from '../constants/AppConstants';

// Dados mock para desenvolvimento

export const mockServiceCategories: ServiceCategory[] = SERVICE_CATEGORIES;

export const mockServices: Service[] = [
  {
    id: '1',
    name: 'Corte Feminino',
    description: 'Corte moderno e estiloso para cabelos femininos',
    duration: 60,
    price: 80,
    category: mockServiceCategories[0],
    image: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?w=300',
    isActive: true,
  },
  {
    id: '2',
    name: 'Coloração Completa',
    description: 'Coloração profissional com produtos de alta qualidade',
    duration: 180,
    price: 200,
    category: mockServiceCategories[0],
    image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=300',
    isActive: true,
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON> Completa',
    description: 'Cuidado completo das unhas das mãos',
    duration: 45,
    price: 35,
    category: mockServiceCategories[1],
    image: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=300',
    isActive: true,
  },
  {
    id: '4',
    name: 'Pedicure Spa',
    description: 'Tratamento relaxante para os pés',
    duration: 60,
    price: 45,
    category: mockServiceCategories[1],
    image: 'https://images.unsplash.com/photo-1519014816548-bf5fe059798b?w=300',
    isActive: true,
  },
  {
    id: '5',
    name: 'Limpeza de Pele',
    description: 'Limpeza profunda e hidratação facial',
    duration: 90,
    price: 120,
    category: mockServiceCategories[2],
    image: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=300',
    isActive: true,
  },
  {
    id: '6',
    name: 'Massagem Relaxante',
    description: 'Massagem corporal para alívio do estresse',
    duration: 60,
    price: 100,
    category: mockServiceCategories[3],
    image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=300',
    isActive: true,
  },
  {
    id: '7',
    name: 'Maquiagem Social',
    description: 'Maquiagem para eventos e ocasiões especiais',
    duration: 45,
    price: 80,
    category: mockServiceCategories[4],
    image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?w=300',
    isActive: true,
  },
];

export const mockProfessionals: Professional[] = [
  {
    id: '1',
    name: 'Ana Silva',
    email: '<EMAIL>',
    phone: '(11) 99999-1111',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
    specialties: ['Corte', 'Coloração', 'Tratamentos'],
    isActive: true,
  },
  {
    id: '2',
    name: 'Carla Santos',
    email: '<EMAIL>',
    phone: '(11) 99999-2222',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
    specialties: ['Unhas', 'Design de Sobrancelhas'],
    isActive: true,
  },
  {
    id: '3',
    name: 'Beatriz Costa',
    email: '<EMAIL>',
    phone: '(11) 99999-3333',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150',
    specialties: ['Estética Facial', 'Massagem'],
    isActive: true,
  },
  {
    id: '4',
    name: 'Daniela Lima',
    email: '<EMAIL>',
    phone: '(11) 99999-4444',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    specialties: ['Maquiagem', 'Penteados'],
    isActive: true,
  },
];

export const mockAppointments: Appointment[] = [
  {
    id: '1',
    userId: 'user1',
    serviceId: '1',
    professionalId: '1',
    date: new Date('2024-01-15'),
    startTime: '10:00',
    endTime: '11:00',
    status: AppointmentStatus.CONFIRMED,
    notes: 'Corte em camadas',
    totalPrice: 80,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
  },
  {
    id: '2',
    userId: 'user1',
    serviceId: '3',
    professionalId: '2',
    date: new Date('2024-01-20'),
    startTime: '14:00',
    endTime: '14:45',
    status: AppointmentStatus.SCHEDULED,
    totalPrice: 35,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12'),
  },
  {
    id: '3',
    userId: 'user1',
    serviceId: '5',
    professionalId: '3',
    date: new Date('2024-01-08'),
    startTime: '15:00',
    endTime: '16:30',
    status: AppointmentStatus.COMPLETED,
    totalPrice: 120,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-08'),
  },
  {
    id: '4',
    userId: 'user2',
    serviceId: '2',
    professionalId: '1',
    date: new Date(),
    startTime: '09:00',
    endTime: '12:00',
    status: AppointmentStatus.COMPLETED,
    totalPrice: 200,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '5',
    userId: 'user3',
    serviceId: '4',
    professionalId: '2',
    date: new Date(),
    startTime: '16:00',
    endTime: '17:00',
    status: AppointmentStatus.CONFIRMED,
    totalPrice: 45,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Dados mock para dashboard administrativo
export const mockAdminDashboard: AdminDashboard = {
  todayStats: {
    appointments: 8,
    revenue: 650,
    newClients: 3,
    completionRate: 87.5,
  },
  weeklyStats: {
    appointments: 45,
    revenue: 3200,
    growth: 12.5,
  },
  monthlyStats: {
    appointments: 180,
    revenue: 14500,
    growth: 8.3,
  },
  recentAppointments: mockAppointments.slice(0, 5),
  topServices: [
    {
      service: mockServices[0],
      totalBookings: 25,
      totalRevenue: 2000,
    },
    {
      service: mockServices[1],
      totalBookings: 15,
      totalRevenue: 3000,
    },
    {
      service: mockServices[2],
      totalBookings: 30,
      totalRevenue: 1050,
    },
  ],
  topProfessionals: [
    {
      professional: mockProfessionals[0],
      totalAppointments: 35,
      totalRevenue: 2800,
      completionRate: 94.3,
    },
    {
      professional: mockProfessionals[1],
      totalAppointments: 28,
      totalRevenue: 1960,
      completionRate: 89.3,
    },
  ],
};