// Constantes do aplicativo de agendamento

export const APP_CONFIG = {
  name: 'Beauty Schedule',
  version: '1.0.0',
  description: 'App de agendamento para salão de beleza',
};

export const SALON_INFO = {
  name: '<PERSON> Vita Salão',
  description: 'Seu salão de beleza de confiança há mais de 10 anos',
  tagline: 'Beleza e bem-estar em um só lugar',
  address: {
    street: 'Rua das Flores, 123',
    neighborhood: 'Centro',
    city: 'São Paulo',
    state: 'SP',
    zipCode: '01234-567',
    full: 'Rua das Flores, 123 - Centro, São Paulo - SP, 01234-567'
  },
  contact: {
    phone: '(11) 3456-7890',
    whatsapp: '(11) 99999-0000',
    email: '<EMAIL>',
    instagram: '@bellavitasalao'
  },
  hours: {
    weekdays: 'Segunda a Sexta: 9h às 18h',
    saturday: 'Sábado: 9h às 17h',
    sunday: 'Domingo: 10h às 16h'
  },
  features: [
    'Profissionais especializados',
    'Produtos de alta qualidade',
    'Ambiente climatizado',
    'Estacionamento gratuito',
    'Wi-Fi liberado',
    'Café e água à vontade'
  ]
};

export const API_CONFIG = {
  baseURL: 'http://localhost:8080/api', // Backend local
  timeout: 10000,
  retryAttempts: 3,
};

export const STORAGE_KEYS = {
  USER_TOKEN: '@beauty_schedule:user_token',
  USER_DATA: '@beauty_schedule:user_data',
  THEME_PREFERENCE: '@beauty_schedule:theme',
  LANGUAGE_PREFERENCE: '@beauty_schedule:language',
};

export const COLORS = {
  primary: '#8B5CF6',
  primaryDark: '#7C3AED',
  primaryLight: '#C4B5FD',
  secondary: '#A855F7',
  secondaryDark: '#9333EA',
  secondaryLight: '#DDD6FE',
  accent: '#EC4899',
  background: '#FFFFFF',
  backgroundLight: '#F9FAFB',
  backgroundDark: '#F8FAFC',
  surface: '#FFFFFF',
  text: '#1F2937',
  textSecondary: '#6B7280',
  textLight: '#FFFFFF',
  white: '#FFFFFF',
  border: '#E5E7EB',
  shadow: '#000000',
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  // Novos gradientes para o design moderno
  gradientStart: '#8B5CF6',
  gradientMiddle: '#A855F7',
  gradientEnd: '#EC4899',
  cardBackground: 'rgba(255, 255, 255, 0.95)',
  cardBorder: 'rgba(139, 92, 246, 0.2)',
};

export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

export const SIZES = {
  // Padding e margin
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,

  // Font sizes
  fontXs: 12,
  fontSm: 14,
  fontMd: 16,
  fontLg: 18,
  fontXl: 20,
  fontXxl: 24,
  fontTitle: 28,

  // Border radius
  radiusXs: 4,
  radiusSm: 8,
  radiusMd: 12,
  radiusLg: 16,
  radiusXl: 24,

  // Icon sizes
  iconXs: 16,
  iconSm: 20,
  iconMd: 24,
  iconLg: 32,
  iconXl: 40,
};

export const BUSINESS_HOURS = {
  monday: { open: '09:00', close: '18:00', isOpen: true },
  tuesday: { open: '09:00', close: '18:00', isOpen: true },
  wednesday: { open: '09:00', close: '18:00', isOpen: true },
  thursday: { open: '09:00', close: '18:00', isOpen: true },
  friday: { open: '09:00', close: '18:00', isOpen: true },
  saturday: { open: '09:00', close: '17:00', isOpen: true },
  sunday: { open: '10:00', close: '16:00', isOpen: false },
};

export const SERVICE_CATEGORIES = [
  {
    id: 'hair',
    name: 'Cabelo',
    icon: 'cut',
    color: COLORS.primary,
  },
  {
    id: 'nails',
    name: 'Unhas',
    icon: 'hand-paper',
    color: COLORS.secondary,
  },
  {
    id: 'facial',
    name: 'Facial',
    icon: 'smile',
    color: COLORS.accent,
  },
  {
    id: 'massage',
    name: 'Massagem',
    icon: 'spa',
    color: COLORS.info,
  },
  {
    id: 'makeup',
    name: 'Maquiagem',
    icon: 'palette',
    color: COLORS.warning,
  },
];

export const APPOINTMENT_DURATION_OPTIONS = [
  { label: '30 min', value: 30 },
  { label: '45 min', value: 45 },
  { label: '1 hora', value: 60 },
  { label: '1h 30min', value: 90 },
  { label: '2 horas', value: 120 },
  { label: '2h 30min', value: 150 },
  { label: '3 horas', value: 180 },
];

export const TIME_SLOTS = [
  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30',
  '15:00', '15:30', '16:00', '16:30', '17:00', '17:30',
];

export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\(\d{2}\)\s\d{4,5}-\d{4}$/,
  password: {
    minLength: 6,
    requireUppercase: false,
    requireLowercase: false,
    requireNumbers: false,
    requireSpecialChars: false,
  },
};

export const MESSAGES = {
  success: {
    appointmentBooked: 'Agendamento realizado com sucesso!',
    appointmentCancelled: 'Agendamento cancelado com sucesso!',
    profileUpdated: 'Perfil atualizado com sucesso!',
    loginSuccess: 'Login realizado com sucesso!',
  },
  error: {
    networkError: 'Erro de conexão. Verifique sua internet.',
    invalidCredentials: 'Email ou senha inválidos.',
    appointmentConflict: 'Horário não disponível.',
    requiredField: 'Este campo é obrigatório.',
    invalidEmail: 'Email inválido.',
    invalidPhone: 'Telefone inválido.',
    passwordTooShort: 'Senha deve ter pelo menos 6 caracteres.',
  },
  info: {
    selectService: 'Selecione um serviço para agendar.',
    selectDateTime: 'Escolha data e horário.',
    noAppointments: 'Você não tem agendamentos.',
    loading: 'Carregando...',
  },
};