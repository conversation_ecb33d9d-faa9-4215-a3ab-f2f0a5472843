import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Button, ServiceCard } from '../../components/common';
import { LoginScreen, RegisterScreen, useAuth } from '../../modules/auth';
import { ServicesScreen } from '../../modules/services';
import { BookingScreen } from '../../modules/booking';
import { AdminDashboardScreen } from '../../modules/admin/screens/AdminDashboardScreen';
import { LoadingScreen } from '../../components/loading/LoadingScreen';
import { mockServices } from '../../data/mockData';
import { Service, UserRole } from '../../types';
import { COLORS, SIZES, FONTS, APP_CONFIG } from '../../constants/AppConstants';

export default function HomeScreen() {
  const {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    pendingServiceId,
    setPendingService,
    clearPendingService
  } = useAuth();

  const [authMode, setAuthMode] = useState<'login' | 'register'>('login');
  const [currentScreen, setCurrentScreen] = useState<'loading' | 'landing' | 'services' | 'booking' | 'home' | 'admin'>('loading');
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  // Efeito para redirecionar após login
  useEffect(() => {
    if (isAuthenticated && pendingServiceId) {
      const service = mockServices.find(s => s.id === pendingServiceId);
      if (service) {
        setSelectedService(service);
        setCurrentScreen('booking');
        clearPendingService();
      }
    } else if (isAuthenticated && currentScreen === 'landing') {
      // Verificar se é admin ou cliente
      if (user?.role === UserRole.ADMIN) {
        setCurrentScreen('admin');
      } else {
        setCurrentScreen('home');
      }
    }
  }, [isAuthenticated, pendingServiceId, user]);

  const handleLoadingComplete = () => {
    setCurrentScreen('landing');
  };

  const handleLogin = async (credentials: any) => {
    const success = await login(credentials);
    if (success) {
      Alert.alert('Sucesso', 'Login realizado com sucesso!');
    }
  };

  const handleRegister = async (userData: any) => {
    const success = await register(userData);
    if (success) {
      Alert.alert('Sucesso', 'Conta criada com sucesso!');
    }
  };

  // Função chamada quando um serviço é selecionado na landing page
  const handleServiceSelect = (service: Service) => {
    if (isAuthenticated) {
      setSelectedService(service);
      setCurrentScreen('booking');
    } else {
      setPendingService(service.id);
      setAuthMode('login');
    }
  };

  // Função para navegar para todos os serviços
  const handleViewAllServices = () => {
    if (isAuthenticated) {
      setCurrentScreen('services');
    } else {
      setAuthMode('login');
    }
  };

  const handleServicePress = (service: Service) => {
    setSelectedService(service);
    setCurrentScreen('booking');
  };

  const handleBookingConfirm = (bookingData: any) => {
    Alert.alert(
      'Agendamento Confirmado!',
      `Seu agendamento foi realizado com sucesso!\n\nServiço: ${bookingData.service.name}\nData: ${bookingData.date.toLocaleDateString('pt-BR')}\nHorário: ${bookingData.time}`,
      [
        {
          text: 'OK',
          onPress: () => setCurrentScreen('home')
        }
      ]
    );
  };

  // Tela de loading inicial
  if (currentScreen === 'loading') {
    return <LoadingScreen onLoadingComplete={handleLoadingComplete} />;
  }

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Carregando...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Renderização das telas de autenticação
  if (!isAuthenticated && (authMode === 'login' || authMode === 'register')) {
    if (authMode === 'login') {
      return (
        <LoginScreen
          onLogin={handleLogin}
          onNavigateToRegister={() => setAuthMode('register')}
          onForgotPassword={() => Alert.alert('Em breve', 'Funcionalidade em desenvolvimento')}
          loading={isLoading}
        />
      );
    } else {
      return (
        <RegisterScreen
          onRegister={handleRegister}
          onNavigateToLogin={() => setAuthMode('login')}
          loading={isLoading}
        />
      );
    }
  }

  // Renderização das telas principais
  switch (currentScreen) {
    case 'services':
      return (
        <ServicesScreen
          onServicePress={handleServicePress}
        />
      );

    case 'booking':
      if (!selectedService) {
        setCurrentScreen('services');
        return null;
      }
      return (
        <BookingScreen
          service={selectedService}
          onGoBack={() => setCurrentScreen(isAuthenticated ? 'home' : 'landing')}
          onBookingConfirm={handleBookingConfirm}
        />
      );

    case 'admin':
      return (
        <AdminDashboardScreen
          onLogout={async () => {
            await logout();
            setCurrentScreen('landing');
          }}
        />
      );

    case 'home':
    default:
      break;
  }

  // Tela principal para usuários autenticados
  const featuredServices = mockServices.slice(0, 3);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>Olá, {user?.name}!</Text>
            <Text style={styles.subtitleText}>Como podemos te ajudar hoje?</Text>
          </View>
          <TouchableOpacity style={styles.profileButton}>
            <Ionicons name="person-circle-outline" size={SIZES.iconLg} color={COLORS.primary} />
          </TouchableOpacity>
        </View>

        <View style={styles.quickActions}>
          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => setCurrentScreen('services')}
          >
            <Ionicons name="calendar-outline" size={SIZES.iconMd} color={COLORS.primary} />
            <Text style={styles.actionText}>Agendar Serviço</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Ionicons name="time-outline" size={SIZES.iconMd} color={COLORS.secondary} />
            <Text style={styles.actionText}>Meus Agendamentos</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Ionicons name="star-outline" size={SIZES.iconMd} color={COLORS.accent} />
            <Text style={styles.actionText}>Favoritos</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Serviços em Destaque</Text>
            <TouchableOpacity onPress={() => setCurrentScreen('services')}>
              <Text style={styles.seeAllText}>Ver todos</Text>
            </TouchableOpacity>
          </View>

          {featuredServices.map((service) => (
            <ServiceCard
              key={service.id}
              service={service}
              onPress={handleServicePress}
              style={styles.serviceCard}
            />
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Próximos Agendamentos</Text>
          <View style={styles.emptyState}>
            <Ionicons name="calendar-outline" size={SIZES.iconXl} color={COLORS.textSecondary} />
            <Text style={styles.emptyStateText}>Nenhum agendamento próximo</Text>
            <Button
              title="Agendar Agora"
              onPress={() => setCurrentScreen('services')}
              variant="outline"
              size="small"
              style={styles.emptyStateButton}
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SIZES.lg,
    paddingBottom: SIZES.md,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    fontSize: SIZES.fontXxl,
    fontFamily: FONTS.bold,
    color: COLORS.text,
    marginBottom: SIZES.xs,
  },
  subtitleText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.regular,
    color: COLORS.textSecondary,
  },
  profileButton: {
    padding: SIZES.sm,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: SIZES.lg,
    marginBottom: SIZES.xl,
  },
  actionCard: {
    flex: 1,
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
    padding: SIZES.lg,
    alignItems: 'center',
    marginHorizontal: SIZES.xs,
    shadowColor: COLORS.text,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.text,
    marginTop: SIZES.sm,
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: SIZES.lg,
    marginBottom: SIZES.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SIZES.md,
  },
  sectionTitle: {
    fontSize: SIZES.fontLg,
    fontFamily: FONTS.bold,
    color: COLORS.text,
  },
  seeAllText: {
    fontSize: SIZES.fontSm,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  serviceCard: {
    marginBottom: SIZES.md,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: SIZES.xl,
    backgroundColor: COLORS.surface,
    borderRadius: SIZES.radiusLg,
  },
  emptyStateText: {
    fontSize: SIZES.fontMd,
    fontFamily: FONTS.medium,
    color: COLORS.textSecondary,
    marginTop: SIZES.md,
    marginBottom: SIZES.lg,
  },
  emptyStateButton: {
    paddingHorizontal: SIZES.xl,
  },
});
