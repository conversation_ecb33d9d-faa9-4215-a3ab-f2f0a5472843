import React, { useState } from 'react';
import { Alert } from 'react-native';
import { ServicesScreen } from '../../modules/services';
import { BookingScreen } from '../../modules/booking';
import { Service } from '../../types';

export default function TabTwoScreen() {
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [showBooking, setShowBooking] = useState(false);

  const handleServicePress = (service: Service) => {
    setSelectedService(service);
    setShowBooking(true);
  };

  const handleBookingConfirm = (bookingData: any) => {
    Alert.alert(
      'Agendamento Confirmado!',
      `Seu agendamento foi realizado com sucesso!\n\nServiço: ${bookingData.service.name}\nData: ${bookingData.date.toLocaleDateString('pt-BR')}\nHorário: ${bookingData.time}`,
      [
        {
          text: 'OK',
          onPress: () => {
            setShowBooking(false);
            setSelectedService(null);
          }
        }
      ]
    );
  };

  if (showBooking && selectedService) {
    return (
      <BookingScreen
        service={selectedService}
        onGoBack={() => {
          setShowBooking(false);
          setSelectedService(null);
        }}
        onBookingConfirm={handleBookingConfirm}
      />
    );
  }

  return (
    <ServicesScreen onServicePress={handleServicePress} />
  );
}


