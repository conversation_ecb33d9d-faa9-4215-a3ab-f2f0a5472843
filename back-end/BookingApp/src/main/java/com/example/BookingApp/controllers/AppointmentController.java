package com.example.BookingApp.controllers;

import com.example.BookingApp.dto.*;
import com.example.BookingApp.enums.AppointmentStatus;
import com.example.BookingApp.services.AppointmentService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/appointments")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AppointmentController {

    private final AppointmentService appointmentService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getAllAppointments() {
        try {
            List<AppointmentDTO> appointments = appointmentService.findAll();
            return ResponseEntity.ok(ApiResponse.success("Agendamentos encontrados com sucesso", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar agendamentos: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<AppointmentDTO>> getAppointmentById(@PathVariable String id) {
        try {
            return appointmentService.findById(id)
                    .map(appointment -> ResponseEntity.ok(ApiResponse.success("Agendamento encontrado", appointment)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Agendamento não encontrado")));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar agendamento: " + e.getMessage()));
        }
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getAppointmentsByUser(@PathVariable String userId) {
        try {
            List<AppointmentDTO> appointments = appointmentService.findByUserId(userId);
            return ResponseEntity.ok(ApiResponse.success("Agendamentos do usuário encontrados", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar agendamentos do usuário: " + e.getMessage()));
        }
    }

    @GetMapping("/user/{userId}/upcoming")
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getUpcomingAppointmentsByUser(@PathVariable String userId) {
        try {
            List<AppointmentDTO> appointments = appointmentService.findUpcomingByUserId(userId);
            return ResponseEntity.ok(ApiResponse.success("Próximos agendamentos do usuário encontrados", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar próximos agendamentos: " + e.getMessage()));
        }
    }

    @GetMapping("/professional/{professionalId}")
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getAppointmentsByProfessional(@PathVariable String professionalId) {
        try {
            List<AppointmentDTO> appointments = appointmentService.findByProfessionalId(professionalId);
            return ResponseEntity.ok(ApiResponse.success("Agendamentos do profissional encontrados", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar agendamentos do profissional: " + e.getMessage()));
        }
    }

    @GetMapping("/professional/{professionalId}/upcoming")
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getUpcomingAppointmentsByProfessional(@PathVariable String professionalId) {
        try {
            List<AppointmentDTO> appointments = appointmentService.findUpcomingByProfessionalId(professionalId);
            return ResponseEntity.ok(ApiResponse.success("Próximos agendamentos do profissional encontrados", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar próximos agendamentos: " + e.getMessage()));
        }
    }

    @GetMapping("/date/{date}")
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getAppointmentsByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            List<AppointmentDTO> appointments = appointmentService.findByDate(date);
            return ResponseEntity.ok(ApiResponse.success("Agendamentos da data encontrados", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar agendamentos da data: " + e.getMessage()));
        }
    }

    @GetMapping("/date-range")
    public ResponseEntity<ApiResponse<List<AppointmentDTO>>> getAppointmentsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        try {
            List<AppointmentDTO> appointments = appointmentService.findByDateRange(startDate, endDate);
            return ResponseEntity.ok(ApiResponse.success("Agendamentos do período encontrados", appointments));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar agendamentos do período: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<AppointmentDTO>> createAppointment(@Valid @RequestBody BookingRequestDTO bookingRequest) {
        try {
            AppointmentDTO createdAppointment = appointmentService.createAppointment(bookingRequest);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Agendamento criado com sucesso", createdAppointment));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao criar agendamento: " + e.getMessage()));
        }
    }

    @PatchMapping("/{id}/status")
    public ResponseEntity<ApiResponse<AppointmentDTO>> updateAppointmentStatus(
            @PathVariable String id, @RequestParam AppointmentStatus status) {
        try {
            AppointmentDTO updatedAppointment = appointmentService.updateStatus(id, status);
            return ResponseEntity.ok(ApiResponse.success("Status do agendamento atualizado", updatedAppointment));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao atualizar status: " + e.getMessage()));
        }
    }

    @PatchMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse<Void>> cancelAppointment(@PathVariable String id) {
        try {
            appointmentService.cancelAppointment(id);
            return ResponseEntity.ok(ApiResponse.success("Agendamento cancelado com sucesso", null));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao cancelar agendamento: " + e.getMessage()));
        }
    }

    @GetMapping("/available-slots")
    public ResponseEntity<ApiResponse<List<TimeSlotDTO>>> getAvailableTimeSlots(
            @RequestParam String professionalId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        try {
            List<TimeSlotDTO> availableSlots = appointmentService.getAvailableTimeSlots(professionalId, date);
            return ResponseEntity.ok(ApiResponse.success("Horários disponíveis encontrados", availableSlots));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar horários disponíveis: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteAppointment(@PathVariable String id) {
        try {
            appointmentService.deleteById(id);
            return ResponseEntity.ok(ApiResponse.success("Agendamento excluído com sucesso", null));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao excluir agendamento: " + e.getMessage()));
        }
    }
}
