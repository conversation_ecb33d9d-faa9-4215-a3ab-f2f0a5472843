package com.example.BookingApp.services;

import com.example.BookingApp.dto.UserDTO;
import com.example.BookingApp.entities.User;
import com.example.BookingApp.mappers.EntityMapper;
import com.example.BookingApp.repositories.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class UserService {

    private final UserRepository userRepository;
    private final EntityMapper entityMapper;

    public List<UserDTO> findAll() {
        return userRepository.findAll()
                .stream()
                .map(entityMapper::toUserDTO)
                .collect(Collectors.toList());
    }

    public Optional<UserDTO> findById(String id) {
        return userRepository.findById(id)
                .map(entityMapper::toUserDTO);
    }

    public Optional<UserDTO> findByEmail(String email) {
        return userRepository.findByEmail(email)
                .map(entityMapper::toUserDTO);
    }

    public UserDTO save(UserDTO userDTO) {
        validateUser(userDTO);

        User user = entityMapper.toUserEntity(userDTO);
        User savedUser = userRepository.save(user);
        return entityMapper.toUserDTO(savedUser);
    }

    public UserDTO saveWithPassword(UserDTO userDTO, String encodedPassword) {
        validateUser(userDTO);

        User user = entityMapper.toUserEntity(userDTO);
        // Senha já vem criptografada do controller
        user.setPassword(encodedPassword);
        User savedUser = userRepository.save(user);
        return entityMapper.toUserDTO(savedUser);
    }

    public Optional<User> findEntityByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    public UserDTO update(String id, UserDTO userDTO) {
        User existingUser = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Usuário não encontrado com ID: " + id));

        validateUserForUpdate(userDTO, id);

        existingUser.setName(userDTO.getName());
        existingUser.setEmail(userDTO.getEmail());
        existingUser.setPhone(userDTO.getPhone());
        existingUser.setAvatar(userDTO.getAvatar());

        User updatedUser = userRepository.save(existingUser);
        return entityMapper.toUserDTO(updatedUser);
    }

    public void deleteById(String id) {
        if (!userRepository.existsById(id)) {
            throw new RuntimeException("Usuário não encontrado com ID: " + id);
        }
        userRepository.deleteById(id);
    }

    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    public boolean existsByPhone(String phone) {
        return userRepository.existsByPhone(phone);
    }

    private void validateUser(UserDTO userDTO) {
        if (userDTO.getEmail() != null && userRepository.existsByEmail(userDTO.getEmail())) {
            throw new RuntimeException("Email já está em uso: " + userDTO.getEmail());
        }
        if (userDTO.getPhone() != null && userRepository.existsByPhone(userDTO.getPhone())) {
            throw new RuntimeException("Telefone já está em uso: " + userDTO.getPhone());
        }
    }

    private void validateUserForUpdate(UserDTO userDTO, String userId) {
        if (userDTO.getEmail() != null) {
            Optional<User> existingUserWithEmail = userRepository.findByEmail(userDTO.getEmail());
            if (existingUserWithEmail.isPresent() && !existingUserWithEmail.get().getId().equals(userId)) {
                throw new RuntimeException("Email já está em uso: " + userDTO.getEmail());
            }
        }
    }
}
