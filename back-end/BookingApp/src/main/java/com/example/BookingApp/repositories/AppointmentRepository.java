package com.example.BookingApp.repositories;

import com.example.BookingApp.entities.Appointment;
import com.example.BookingApp.entities.Professional;
import com.example.BookingApp.entities.User;
import com.example.BookingApp.enums.AppointmentStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Repository
public interface AppointmentRepository extends JpaRepository<Appointment, String> {

    List<Appointment> findByUser(User user);

    List<Appointment> findByUserId(String userId);

    List<Appointment> findByProfessional(Professional professional);

    List<Appointment> findByProfessionalId(String professionalId);

    List<Appointment> findByStatus(AppointmentStatus status);

    List<Appointment> findByDate(LocalDate date);

    List<Appointment> findByDateBetween(LocalDate startDate, LocalDate endDate);

    @Query("SELECT a FROM Appointment a WHERE a.professional.id = :professionalId AND a.date = :date")
    List<Appointment> findByProfessionalIdAndDate(@Param("professionalId") String professionalId, 
                                                  @Param("date") LocalDate date);

    @Query("SELECT a FROM Appointment a WHERE a.professional.id = :professionalId AND a.date = :date AND " +
           "((a.startTime <= :startTime AND a.endTime > :startTime) OR " +
           "(a.startTime < :endTime AND a.endTime >= :endTime) OR " +
           "(a.startTime >= :startTime AND a.endTime <= :endTime)) AND " +
           "a.status NOT IN ('CANCELLED', 'NO_SHOW')")
    List<Appointment> findConflictingAppointments(@Param("professionalId") String professionalId,
                                                  @Param("date") LocalDate date,
                                                  @Param("startTime") LocalTime startTime,
                                                  @Param("endTime") LocalTime endTime);

    @Query("SELECT a FROM Appointment a WHERE a.user.id = :userId AND a.date >= :date ORDER BY a.date ASC, a.startTime ASC")
    List<Appointment> findUpcomingAppointmentsByUserId(@Param("userId") String userId, @Param("date") LocalDate date);

    @Query("SELECT a FROM Appointment a WHERE a.professional.id = :professionalId AND a.date >= :date ORDER BY a.date ASC, a.startTime ASC")
    List<Appointment> findUpcomingAppointmentsByProfessionalId(@Param("professionalId") String professionalId, @Param("date") LocalDate date);
}
