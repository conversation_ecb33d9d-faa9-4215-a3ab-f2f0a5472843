package com.example.BookingApp.mappers;

import com.example.BookingApp.dto.*;
import com.example.BookingApp.entities.*;
import org.springframework.stereotype.Component;

@Component
public class EntityMapper {

    public UserDTO toUserDTO(User user) {
        if (user == null) return null;
        return new UserDTO(
                user.getId(),
                user.getName(),
                user.getEmail(),
                user.getPhone(),
                user.getAvatar(),
                user.getCreatedAt(),
                user.getUpdatedAt()
        );
    }

    public User toUserEntity(UserDTO userDTO) {
        if (userDTO == null) return null;
        User user = new User();
        user.setId(userDTO.getId());
        user.setName(userDTO.getName());
        user.setEmail(userDTO.getEmail());
        user.setPhone(userDTO.getPhone());
        user.setAvatar(userDTO.getAvatar());
        return user;
    }

    public ServiceCategoryDTO toServiceCategoryDTO(ServiceCategory category) {
        if (category == null) return null;
        return new ServiceCategoryDTO(
                category.getId(),
                category.getName(),
                category.getDescription(),
                category.getIcon(),
                category.getColor()
        );
    }

    public ServiceCategory toServiceCategoryEntity(ServiceCategoryDTO categoryDTO) {
        if (categoryDTO == null) return null;
        ServiceCategory category = new ServiceCategory();
        category.setId(categoryDTO.getId());
        category.setName(categoryDTO.getName());
        category.setDescription(categoryDTO.getDescription());
        category.setIcon(categoryDTO.getIcon());
        category.setColor(categoryDTO.getColor());
        return category;
    }

    public ServiceDTO toServiceDTO(Service service) {
        if (service == null) return null;
        return new ServiceDTO(
                service.getId(),
                service.getName(),
                service.getDescription(),
                service.getDuration(),
                service.getPrice(),
                service.getImage(),
                service.getIsActive(),
                toServiceCategoryDTO(service.getCategory())
        );
    }

    public Service toServiceEntity(ServiceDTO serviceDTO) {
        if (serviceDTO == null) return null;
        Service service = new Service();
        service.setId(serviceDTO.getId());
        service.setName(serviceDTO.getName());
        service.setDescription(serviceDTO.getDescription());
        service.setDuration(serviceDTO.getDuration());
        service.setPrice(serviceDTO.getPrice());
        service.setImage(serviceDTO.getImage());
        service.setIsActive(serviceDTO.getIsActive());
        service.setCategory(toServiceCategoryEntity(serviceDTO.getCategory()));
        return service;
    }

    public ProfessionalDTO toProfessionalDTO(Professional professional) {
        if (professional == null) return null;
        return new ProfessionalDTO(
                professional.getId(),
                professional.getName(),
                professional.getEmail(),
                professional.getPhone(),
                professional.getAvatar(),
                professional.getSpecialties(),
                professional.getIsActive()
        );
    }

    public Professional toProfessionalEntity(ProfessionalDTO professionalDTO) {
        if (professionalDTO == null) return null;
        Professional professional = new Professional();
        professional.setId(professionalDTO.getId());
        professional.setName(professionalDTO.getName());
        professional.setEmail(professionalDTO.getEmail());
        professional.setPhone(professionalDTO.getPhone());
        professional.setAvatar(professionalDTO.getAvatar());
        professional.setSpecialties(professionalDTO.getSpecialties());
        professional.setIsActive(professionalDTO.getIsActive());
        return professional;
    }

    public AppointmentDTO toAppointmentDTO(Appointment appointment) {
        if (appointment == null) return null;
        return new AppointmentDTO(
                appointment.getId(),
                toUserDTO(appointment.getUser()),
                toServiceDTO(appointment.getService()),
                toProfessionalDTO(appointment.getProfessional()),
                appointment.getDate(),
                appointment.getStartTime(),
                appointment.getEndTime(),
                appointment.getStatus(),
                appointment.getNotes(),
                appointment.getTotalPrice(),
                appointment.getCreatedAt(),
                appointment.getUpdatedAt()
        );
    }
}
