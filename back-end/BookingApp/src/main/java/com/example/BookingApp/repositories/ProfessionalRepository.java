package com.example.BookingApp.repositories;

import com.example.BookingApp.entities.Professional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProfessionalRepository extends JpaRepository<Professional, String> {

    List<Professional> findByIsActiveTrue();

    Optional<Professional> findByEmail(String email);

    boolean existsByEmail(String email);

    @Query("SELECT p FROM Professional p WHERE p.isActive = true AND " +
           ":specialty MEMBER OF p.specialties")
    List<Professional> findBySpecialtyAndIsActiveTrue(@Param("specialty") String specialty);

    @Query("SELECT p FROM Professional p WHERE p.isActive = true AND " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :search, '%'))")
    List<Professional> findByNameContainingIgnoreCaseAndIsActiveTrue(@Param("search") String search);
}
