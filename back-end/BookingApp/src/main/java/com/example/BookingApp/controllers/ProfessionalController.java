package com.example.BookingApp.controllers;

import com.example.BookingApp.dto.ApiResponse;
import com.example.BookingApp.dto.ProfessionalDTO;
import com.example.BookingApp.services.ProfessionalService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/professionals")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ProfessionalController {

    private final ProfessionalService professionalService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<ProfessionalDTO>>> getAllProfessionals() {
        try {
            List<ProfessionalDTO> professionals = professionalService.findAll();
            return ResponseEntity.ok(ApiResponse.success("Profissionais encontrados com sucesso", professionals));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar profissionais: " + e.getMessage()));
        }
    }

    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<ProfessionalDTO>>> getAllActiveProfessionals() {
        try {
            List<ProfessionalDTO> professionals = professionalService.findAllActive();
            return ResponseEntity.ok(ApiResponse.success("Profissionais ativos encontrados com sucesso", professionals));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar profissionais ativos: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ProfessionalDTO>> getProfessionalById(@PathVariable String id) {
        try {
            return professionalService.findById(id)
                    .map(professional -> ResponseEntity.ok(ApiResponse.success("Profissional encontrado", professional)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Profissional não encontrado")));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar profissional: " + e.getMessage()));
        }
    }

    @GetMapping("/email/{email}")
    public ResponseEntity<ApiResponse<ProfessionalDTO>> getProfessionalByEmail(@PathVariable String email) {
        try {
            return professionalService.findByEmail(email)
                    .map(professional -> ResponseEntity.ok(ApiResponse.success("Profissional encontrado", professional)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Profissional não encontrado")));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar profissional: " + e.getMessage()));
        }
    }

    @GetMapping("/specialty/{specialty}")
    public ResponseEntity<ApiResponse<List<ProfessionalDTO>>> getProfessionalsBySpecialty(@PathVariable String specialty) {
        try {
            List<ProfessionalDTO> professionals = professionalService.findBySpecialty(specialty);
            return ResponseEntity.ok(ApiResponse.success("Profissionais da especialidade encontrados", professionals));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar profissionais da especialidade: " + e.getMessage()));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<ProfessionalDTO>>> searchProfessionals(@RequestParam String name) {
        try {
            List<ProfessionalDTO> professionals = professionalService.searchByName(name);
            return ResponseEntity.ok(ApiResponse.success("Busca realizada com sucesso", professionals));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar profissionais: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<ProfessionalDTO>> createProfessional(@Valid @RequestBody ProfessionalDTO professionalDTO) {
        try {
            ProfessionalDTO createdProfessional = professionalService.save(professionalDTO);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Profissional criado com sucesso", createdProfessional));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao criar profissional: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ProfessionalDTO>> updateProfessional(@PathVariable String id, @Valid @RequestBody ProfessionalDTO professionalDTO) {
        try {
            ProfessionalDTO updatedProfessional = professionalService.update(id, professionalDTO);
            return ResponseEntity.ok(ApiResponse.success("Profissional atualizado com sucesso", updatedProfessional));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao atualizar profissional: " + e.getMessage()));
        }
    }

    @PatchMapping("/{id}/toggle-active")
    public ResponseEntity<ApiResponse<ProfessionalDTO>> toggleProfessionalActive(@PathVariable String id) {
        try {
            ProfessionalDTO updatedProfessional = professionalService.toggleActive(id);
            return ResponseEntity.ok(ApiResponse.success("Status do profissional alterado com sucesso", updatedProfessional));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao alterar status do profissional: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteProfessional(@PathVariable String id) {
        try {
            professionalService.deleteById(id);
            return ResponseEntity.ok(ApiResponse.success("Profissional excluído com sucesso", null));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao excluir profissional: " + e.getMessage()));
        }
    }

    @GetMapping("/exists/email/{email}")
    public ResponseEntity<ApiResponse<Boolean>> checkEmailExists(@PathVariable String email) {
        try {
            boolean exists = professionalService.existsByEmail(email);
            return ResponseEntity.ok(ApiResponse.success("Verificação realizada", exists));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao verificar email: " + e.getMessage()));
        }
    }
}
