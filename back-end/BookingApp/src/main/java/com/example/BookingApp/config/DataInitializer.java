package com.example.BookingApp.config;

import com.example.BookingApp.entities.*;
import com.example.BookingApp.repositories.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.*;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final ServiceCategoryRepository serviceCategoryRepository;
    private final ServiceRepository serviceRepository;
    private final ProfessionalRepository professionalRepository;
    private final UserRepository userRepository;

    @Override
    public void run(String... args) throws Exception {
        if (serviceCategoryRepository.count() == 0) {
            log.info("Inicializando dados do banco...");
            initializeData();
            log.info("Dados inicializados com sucesso!");
        } else {
            log.info("Dados já existem no banco, pulando inicialização.");
        }
    }

    private void initializeData() {
        // Criar categorias
        List<ServiceCategory> categories = createCategories();
        
        // Criar serviços
        createServices(categories);
        
        // Criar profissionais
        createProfessionals();
        
        // Criar usuários de exemplo
        createUsers();
    }

    private List<ServiceCategory> createCategories() {
        ServiceCategory hair = new ServiceCategory();
        hair.setName("Cabelo");
        hair.setDescription("Serviços para cabelo");
        hair.setIcon("cut");
        hair.setColor("#FF6B6B");

        ServiceCategory nails = new ServiceCategory();
        nails.setName("Unhas");
        nails.setDescription("Serviços de manicure e pedicure");
        nails.setIcon("hand-paper");
        nails.setColor("#4ECDC4");

        ServiceCategory facial = new ServiceCategory();
        facial.setName("Facial");
        facial.setDescription("Tratamentos faciais");
        facial.setIcon("smile");
        facial.setColor("#45B7D1");

        ServiceCategory massage = new ServiceCategory();
        massage.setName("Massagem");
        massage.setDescription("Massagens relaxantes");
        massage.setIcon("spa");
        massage.setColor("#96CEB4");

        ServiceCategory makeup = new ServiceCategory();
        makeup.setName("Maquiagem");
        makeup.setDescription("Serviços de maquiagem");
        makeup.setIcon("palette");
        makeup.setColor("#FFEAA7");

        List<ServiceCategory> categories = Arrays.asList(hair, nails, facial, massage, makeup);
        return serviceCategoryRepository.saveAll(categories);
    }

    private void createServices(List<ServiceCategory> categories) {
        ServiceCategory hair = categories.get(0);
        ServiceCategory nails = categories.get(1);
        ServiceCategory facial = categories.get(2);
        ServiceCategory massage = categories.get(3);
        ServiceCategory makeup = categories.get(4);

        // Serviços de cabelo
        Service haircut = new Service();
        haircut.setName("Corte de Cabelo");
        haircut.setDescription("Corte personalizado de acordo com o formato do rosto");
        haircut.setDuration(60);
        haircut.setPrice(new BigDecimal("80.00"));
        haircut.setCategory(hair);
        haircut.setIsActive(true);

        Service hairWash = new Service();
        hairWash.setName("Lavagem e Escova");
        hairWash.setDescription("Lavagem com produtos profissionais e escova modeladora");
        hairWash.setDuration(45);
        hairWash.setPrice(new BigDecimal("50.00"));
        hairWash.setCategory(hair);
        hairWash.setIsActive(true);

        Service hairColor = new Service();
        hairColor.setName("Coloração");
        hairColor.setDescription("Coloração completa com produtos de alta qualidade");
        hairColor.setDuration(120);
        hairColor.setPrice(new BigDecimal("150.00"));
        hairColor.setCategory(hair);
        hairColor.setIsActive(true);

        // Serviços de unhas
        Service manicure = new Service();
        manicure.setName("Manicure");
        manicure.setDescription("Cuidados completos para as unhas das mãos");
        manicure.setDuration(45);
        manicure.setPrice(new BigDecimal("35.00"));
        manicure.setCategory(nails);
        manicure.setIsActive(true);

        Service pedicure = new Service();
        pedicure.setName("Pedicure");
        pedicure.setDescription("Cuidados completos para as unhas dos pés");
        pedicure.setDuration(60);
        pedicure.setPrice(new BigDecimal("40.00"));
        pedicure.setCategory(nails);
        pedicure.setIsActive(true);

        // Serviços faciais
        Service facialCleaning = new Service();
        facialCleaning.setName("Limpeza de Pele");
        facialCleaning.setDescription("Limpeza profunda com extração de cravos");
        facialCleaning.setDuration(90);
        facialCleaning.setPrice(new BigDecimal("120.00"));
        facialCleaning.setCategory(facial);
        facialCleaning.setIsActive(true);

        // Serviços de massagem
        Service relaxingMassage = new Service();
        relaxingMassage.setName("Massagem Relaxante");
        relaxingMassage.setDescription("Massagem corporal para alívio do estresse");
        relaxingMassage.setDuration(60);
        relaxingMassage.setPrice(new BigDecimal("100.00"));
        relaxingMassage.setCategory(massage);
        relaxingMassage.setIsActive(true);

        // Serviços de maquiagem
        Service socialMakeup = new Service();
        socialMakeup.setName("Maquiagem Social");
        socialMakeup.setDescription("Maquiagem para eventos sociais");
        socialMakeup.setDuration(45);
        socialMakeup.setPrice(new BigDecimal("80.00"));
        socialMakeup.setCategory(makeup);
        socialMakeup.setIsActive(true);

        List<Service> services = Arrays.asList(
                haircut, hairWash, hairColor, manicure, pedicure, 
                facialCleaning, relaxingMassage, socialMakeup
        );
        serviceRepository.saveAll(services);
    }

    private void createProfessionals() {
        Professional maria = new Professional();
        maria.setName("Maria Silva");
        maria.setEmail("<EMAIL>");
        maria.setPhone("(11) 99999-0001");
        maria.setSpecialties(Arrays.asList("Cabelo", "Maquiagem"));
        maria.setIsActive(true);

        Professional ana = new Professional();
        ana.setName("Ana Santos");
        ana.setEmail("<EMAIL>");
        ana.setPhone("(11) 99999-0002");
        ana.setSpecialties(Arrays.asList("Unhas", "Facial"));
        ana.setIsActive(true);

        Professional carla = new Professional();
        carla.setName("Carla Oliveira");
        carla.setEmail("<EMAIL>");
        carla.setPhone("(11) 99999-0003");
        carla.setSpecialties(Arrays.asList("Massagem", "Facial"));
        carla.setIsActive(true);

        List<Professional> professionals = Arrays.asList(maria, ana, carla);
        professionalRepository.saveAll(professionals);
    }

    private void createUsers() {
        User user1 = new User();
        user1.setName("João Silva");
        user1.setEmail("<EMAIL>");
        user1.setPhone("(11) 98888-0001");
        user1.setPassword("123456");

        User user2 = new User();
        user2.setName("Maria Oliveira");
        user2.setEmail("<EMAIL>");
        user2.setPhone("(11) 98888-0002");
        user2.setPassword("123456");

        List<User> users = Arrays.asList(user1, user2);
        userRepository.saveAll(users);
    }
}
