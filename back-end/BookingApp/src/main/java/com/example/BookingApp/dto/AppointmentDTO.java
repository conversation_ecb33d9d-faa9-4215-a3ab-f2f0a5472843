package com.example.BookingApp.dto;

import com.example.BookingApp.enums.AppointmentStatus;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentDTO {
    private String id;

    @NotNull(message = "Usuário é obrigatório")
    private UserDTO user;

    @NotNull(message = "Serviço é obrigatório")
    private ServiceDTO service;

    @NotNull(message = "Profissional é obrigatório")
    private ProfessionalDTO professional;

    @NotNull(message = "Data é obrigatória")
    private LocalDate date;

    @NotNull(message = "Horário de início é obrigatório")
    private LocalTime startTime;

    @NotNull(message = "Horário de fim é obrigatório")
    private LocalTime endTime;

    private AppointmentStatus status;

    @Size(max = 500, message = "Observações devem ter no máximo 500 caracteres")
    private String notes;

    @NotNull(message = "Preço total é obrigatório")
    @DecimalMin(value = "0.01", message = "Preço total deve ser maior que zero")
    private BigDecimal totalPrice;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
