package com.example.BookingApp.services;

import com.example.BookingApp.dto.*;
import com.example.BookingApp.entities.*;
import com.example.BookingApp.enums.AppointmentStatus;
import com.example.BookingApp.mappers.EntityMapper;
import com.example.BookingApp.repositories.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class AppointmentService {

    private final AppointmentRepository appointmentRepository;
    private final UserRepository userRepository;
    private final ServiceRepository serviceRepository;
    private final ProfessionalRepository professionalRepository;
    private final EntityMapper entityMapper;

    // Horários disponíveis (9:00 às 18:00, intervalos de 30 minutos)
    private final List<LocalTime> TIME_SLOTS = List.of(
            LocalTime.of(9, 0), LocalTime.of(9, 30), LocalTime.of(10, 0), LocalTime.of(10, 30),
            LocalTime.of(11, 0), LocalTime.of(11, 30), LocalTime.of(12, 0), LocalTime.of(12, 30),
            LocalTime.of(13, 0), LocalTime.of(13, 30), LocalTime.of(14, 0), LocalTime.of(14, 30),
            LocalTime.of(15, 0), LocalTime.of(15, 30), LocalTime.of(16, 0), LocalTime.of(16, 30),
            LocalTime.of(17, 0), LocalTime.of(17, 30)
    );

    public List<AppointmentDTO> findAll() {
        return appointmentRepository.findAll()
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public Optional<AppointmentDTO> findById(String id) {
        return appointmentRepository.findById(id)
                .map(entityMapper::toAppointmentDTO);
    }

    public List<AppointmentDTO> findByUserId(String userId) {
        return appointmentRepository.findByUserId(userId)
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public List<AppointmentDTO> findByProfessionalId(String professionalId) {
        return appointmentRepository.findByProfessionalId(professionalId)
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public List<AppointmentDTO> findUpcomingByUserId(String userId) {
        return appointmentRepository.findUpcomingAppointmentsByUserId(userId, LocalDate.now())
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public List<AppointmentDTO> findUpcomingByProfessionalId(String professionalId) {
        return appointmentRepository.findUpcomingAppointmentsByProfessionalId(professionalId, LocalDate.now())
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public List<AppointmentDTO> findByDate(LocalDate date) {
        return appointmentRepository.findByDate(date)
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public List<AppointmentDTO> findByDateRange(LocalDate startDate, LocalDate endDate) {
        return appointmentRepository.findByDateBetween(startDate, endDate)
                .stream()
                .map(entityMapper::toAppointmentDTO)
                .collect(Collectors.toList());
    }

    public AppointmentDTO createAppointment(BookingRequestDTO bookingRequest) {
        // Validar dados
        User user = userRepository.findById(bookingRequest.getUserId())
                .orElseThrow(() -> new RuntimeException("Usuário não encontrado"));
        
        com.example.BookingApp.entities.Service service = serviceRepository.findById(bookingRequest.getServiceId())
                .orElseThrow(() -> new RuntimeException("Serviço não encontrado"));
        
        Professional professional = professionalRepository.findById(bookingRequest.getProfessionalId())
                .orElseThrow(() -> new RuntimeException("Profissional não encontrado"));

        // Calcular horário de fim baseado na duração do serviço
        LocalTime endTime = bookingRequest.getTime().plusMinutes(service.getDuration());

        // Verificar disponibilidade
        if (!isTimeSlotAvailable(bookingRequest.getProfessionalId(), bookingRequest.getDate(), 
                                bookingRequest.getTime(), endTime)) {
            throw new RuntimeException("Horário não disponível");
        }

        // Criar agendamento
        Appointment appointment = new Appointment();
        appointment.setUser(user);
        appointment.setService(service);
        appointment.setProfessional(professional);
        appointment.setDate(bookingRequest.getDate());
        appointment.setStartTime(bookingRequest.getTime());
        appointment.setEndTime(endTime);
        appointment.setStatus(AppointmentStatus.SCHEDULED);
        appointment.setNotes(bookingRequest.getNotes());
        appointment.setTotalPrice(service.getPrice());

        Appointment savedAppointment = appointmentRepository.save(appointment);
        return entityMapper.toAppointmentDTO(savedAppointment);
    }

    public AppointmentDTO updateStatus(String id, AppointmentStatus status) {
        Appointment appointment = appointmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Agendamento não encontrado"));
        
        appointment.setStatus(status);
        Appointment updatedAppointment = appointmentRepository.save(appointment);
        return entityMapper.toAppointmentDTO(updatedAppointment);
    }

    public void cancelAppointment(String id) {
        updateStatus(id, AppointmentStatus.CANCELLED);
    }

    public List<TimeSlotDTO> getAvailableTimeSlots(String professionalId, LocalDate date) {
        List<TimeSlotDTO> availableSlots = new ArrayList<>();
        
        // Buscar agendamentos existentes para o profissional na data
        List<Appointment> existingAppointments = appointmentRepository.findByProfessionalIdAndDate(professionalId, date);
        
        for (LocalTime timeSlot : TIME_SLOTS) {
            boolean isAvailable = existingAppointments.stream()
                    .noneMatch(appointment -> 
                        (timeSlot.equals(appointment.getStartTime()) || 
                         (timeSlot.isAfter(appointment.getStartTime()) && timeSlot.isBefore(appointment.getEndTime()))) &&
                        !appointment.getStatus().equals(AppointmentStatus.CANCELLED) &&
                        !appointment.getStatus().equals(AppointmentStatus.NO_SHOW)
                    );
            
            availableSlots.add(new TimeSlotDTO(timeSlot, isAvailable, professionalId));
        }
        
        return availableSlots;
    }

    private boolean isTimeSlotAvailable(String professionalId, LocalDate date, LocalTime startTime, LocalTime endTime) {
        List<Appointment> conflictingAppointments = appointmentRepository.findConflictingAppointments(
                professionalId, date, startTime, endTime);
        return conflictingAppointments.isEmpty();
    }

    public void deleteById(String id) {
        if (!appointmentRepository.existsById(id)) {
            throw new RuntimeException("Agendamento não encontrado com ID: " + id);
        }
        appointmentRepository.deleteById(id);
    }
}
