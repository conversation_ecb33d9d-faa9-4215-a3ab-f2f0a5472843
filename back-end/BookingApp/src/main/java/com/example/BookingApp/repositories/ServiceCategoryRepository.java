package com.example.BookingApp.repositories;

import com.example.BookingApp.entities.ServiceCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ServiceCategoryRepository extends JpaRepository<ServiceCategory, String> {

    Optional<ServiceCategory> findByName(String name);

    boolean existsByName(String name);
}
