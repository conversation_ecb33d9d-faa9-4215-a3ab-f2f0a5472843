package com.example.BookingApp.services;

import com.example.BookingApp.dto.ProfessionalDTO;
import com.example.BookingApp.entities.Professional;
import com.example.BookingApp.mappers.EntityMapper;
import com.example.BookingApp.repositories.ProfessionalRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProfessionalService {

    private final ProfessionalRepository professionalRepository;
    private final EntityMapper entityMapper;

    public List<ProfessionalDTO> findAll() {
        return professionalRepository.findAll()
                .stream()
                .map(entityMapper::toProfessionalDTO)
                .collect(Collectors.toList());
    }

    public List<ProfessionalDTO> findAllActive() {
        return professionalRepository.findByIsActiveTrue()
                .stream()
                .map(entityMapper::toProfessionalDTO)
                .collect(Collectors.toList());
    }

    public Optional<ProfessionalDTO> findById(String id) {
        return professionalRepository.findById(id)
                .map(entityMapper::toProfessionalDTO);
    }

    public Optional<ProfessionalDTO> findByEmail(String email) {
        return professionalRepository.findByEmail(email)
                .map(entityMapper::toProfessionalDTO);
    }

    public List<ProfessionalDTO> findBySpecialty(String specialty) {
        return professionalRepository.findBySpecialtyAndIsActiveTrue(specialty)
                .stream()
                .map(entityMapper::toProfessionalDTO)
                .collect(Collectors.toList());
    }

    public List<ProfessionalDTO> searchByName(String name) {
        return professionalRepository.findByNameContainingIgnoreCaseAndIsActiveTrue(name)
                .stream()
                .map(entityMapper::toProfessionalDTO)
                .collect(Collectors.toList());
    }

    public ProfessionalDTO save(ProfessionalDTO professionalDTO) {
        validateProfessional(professionalDTO);
        
        Professional professional = entityMapper.toProfessionalEntity(professionalDTO);
        Professional savedProfessional = professionalRepository.save(professional);
        return entityMapper.toProfessionalDTO(savedProfessional);
    }

    public ProfessionalDTO update(String id, ProfessionalDTO professionalDTO) {
        Professional existingProfessional = professionalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Profissional não encontrado com ID: " + id));

        validateProfessionalForUpdate(professionalDTO, id);

        existingProfessional.setName(professionalDTO.getName());
        existingProfessional.setEmail(professionalDTO.getEmail());
        existingProfessional.setPhone(professionalDTO.getPhone());
        existingProfessional.setAvatar(professionalDTO.getAvatar());
        existingProfessional.setSpecialties(professionalDTO.getSpecialties());
        existingProfessional.setIsActive(professionalDTO.getIsActive());

        Professional updatedProfessional = professionalRepository.save(existingProfessional);
        return entityMapper.toProfessionalDTO(updatedProfessional);
    }

    public void deleteById(String id) {
        if (!professionalRepository.existsById(id)) {
            throw new RuntimeException("Profissional não encontrado com ID: " + id);
        }
        professionalRepository.deleteById(id);
    }

    public ProfessionalDTO toggleActive(String id) {
        Professional professional = professionalRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Profissional não encontrado com ID: " + id));
        
        professional.setIsActive(!professional.getIsActive());
        Professional updatedProfessional = professionalRepository.save(professional);
        return entityMapper.toProfessionalDTO(updatedProfessional);
    }

    public boolean existsByEmail(String email) {
        return professionalRepository.existsByEmail(email);
    }

    private void validateProfessional(ProfessionalDTO professionalDTO) {
        if (professionalDTO.getEmail() != null && professionalRepository.existsByEmail(professionalDTO.getEmail())) {
            throw new RuntimeException("Email já está em uso: " + professionalDTO.getEmail());
        }
    }

    private void validateProfessionalForUpdate(ProfessionalDTO professionalDTO, String professionalId) {
        if (professionalDTO.getEmail() != null) {
            Optional<Professional> existingProfessionalWithEmail = professionalRepository.findByEmail(professionalDTO.getEmail());
            if (existingProfessionalWithEmail.isPresent() && !existingProfessionalWithEmail.get().getId().equals(professionalId)) {
                throw new RuntimeException("Email já está em uso: " + professionalDTO.getEmail());
            }
        }
    }
}
