package com.example.BookingApp.repositories;

import com.example.BookingApp.entities.Service;
import com.example.BookingApp.entities.ServiceCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServiceRepository extends JpaRepository<Service, String> {

    List<Service> findByIsActiveTrue();

    List<Service> findByCategoryAndIsActiveTrue(ServiceCategory category);

    List<Service> findByCategoryIdAndIsActiveTrue(String categoryId);

    @Query("SELECT s FROM Service s WHERE s.isActive = true AND " +
           "(LOWER(s.name) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(s.description) LIKE LOWER(CONCAT('%', :search, '%')))")
    List<Service> findByNameOrDescriptionContainingIgnoreCaseAndIsActiveTrue(@Param("search") String search);

    boolean existsByName(String name);
}
