package com.example.BookingApp.services;

import com.example.BookingApp.dto.ServiceCategoryDTO;
import com.example.BookingApp.entities.ServiceCategory;
import com.example.BookingApp.mappers.EntityMapper;
import com.example.BookingApp.repositories.ServiceCategoryRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ServiceCategoryService {

    private final ServiceCategoryRepository serviceCategoryRepository;
    private final EntityMapper entityMapper;

    public List<ServiceCategoryDTO> findAll() {
        return serviceCategoryRepository.findAll()
                .stream()
                .map(entityMapper::toServiceCategoryDTO)
                .collect(Collectors.toList());
    }

    public Optional<ServiceCategoryDTO> findById(String id) {
        return serviceCategoryRepository.findById(id)
                .map(entityMapper::toServiceCategoryDTO);
    }

    public Optional<ServiceCategoryDTO> findByName(String name) {
        return serviceCategoryRepository.findByName(name)
                .map(entityMapper::toServiceCategoryDTO);
    }

    public ServiceCategoryDTO save(ServiceCategoryDTO categoryDTO) {
        validateCategory(categoryDTO);
        
        ServiceCategory category = entityMapper.toServiceCategoryEntity(categoryDTO);
        ServiceCategory savedCategory = serviceCategoryRepository.save(category);
        return entityMapper.toServiceCategoryDTO(savedCategory);
    }

    public ServiceCategoryDTO update(String id, ServiceCategoryDTO categoryDTO) {
        ServiceCategory existingCategory = serviceCategoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Categoria não encontrada com ID: " + id));

        validateCategoryForUpdate(categoryDTO, id);

        existingCategory.setName(categoryDTO.getName());
        existingCategory.setDescription(categoryDTO.getDescription());
        existingCategory.setIcon(categoryDTO.getIcon());
        existingCategory.setColor(categoryDTO.getColor());

        ServiceCategory updatedCategory = serviceCategoryRepository.save(existingCategory);
        return entityMapper.toServiceCategoryDTO(updatedCategory);
    }

    public void deleteById(String id) {
        if (!serviceCategoryRepository.existsById(id)) {
            throw new RuntimeException("Categoria não encontrada com ID: " + id);
        }
        serviceCategoryRepository.deleteById(id);
    }

    public boolean existsByName(String name) {
        return serviceCategoryRepository.existsByName(name);
    }

    private void validateCategory(ServiceCategoryDTO categoryDTO) {
        if (categoryDTO.getName() != null && serviceCategoryRepository.existsByName(categoryDTO.getName())) {
            throw new RuntimeException("Categoria já existe com o nome: " + categoryDTO.getName());
        }
    }

    private void validateCategoryForUpdate(ServiceCategoryDTO categoryDTO, String categoryId) {
        if (categoryDTO.getName() != null) {
            Optional<ServiceCategory> existingCategoryWithName = serviceCategoryRepository.findByName(categoryDTO.getName());
            if (existingCategoryWithName.isPresent() && !existingCategoryWithName.get().getId().equals(categoryId)) {
                throw new RuntimeException("Categoria já existe com o nome: " + categoryDTO.getName());
            }
        }
    }
}
