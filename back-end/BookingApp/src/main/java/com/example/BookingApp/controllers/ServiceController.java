package com.example.BookingApp.controllers;

import com.example.BookingApp.dto.ApiResponse;
import com.example.BookingApp.dto.ServiceDTO;
import com.example.BookingApp.services.ServiceService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/services")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ServiceController {

    private final ServiceService serviceService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> getAllServices() {
        try {
            List<ServiceDTO> services = serviceService.findAll();
            return ResponseEntity.ok(ApiResponse.success("Serviços encontrados com sucesso", services));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar serviços: " + e.getMessage()));
        }
    }

    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> getAllActiveServices() {
        try {
            List<ServiceDTO> services = serviceService.findAllActive();
            return ResponseEntity.ok(ApiResponse.success("Serviços ativos encontrados com sucesso", services));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar serviços ativos: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ServiceDTO>> getServiceById(@PathVariable String id) {
        try {
            return serviceService.findById(id)
                    .map(service -> ResponseEntity.ok(ApiResponse.success("Serviço encontrado", service)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Serviço não encontrado")));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar serviço: " + e.getMessage()));
        }
    }

    @GetMapping("/category/{categoryId}")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> getServicesByCategory(@PathVariable String categoryId) {
        try {
            List<ServiceDTO> services = serviceService.findByCategoryId(categoryId);
            return ResponseEntity.ok(ApiResponse.success("Serviços da categoria encontrados", services));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar serviços da categoria: " + e.getMessage()));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> searchServices(@RequestParam String query) {
        try {
            List<ServiceDTO> services = serviceService.searchByNameOrDescription(query);
            return ResponseEntity.ok(ApiResponse.success("Busca realizada com sucesso", services));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar serviços: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<ServiceDTO>> createService(@Valid @RequestBody ServiceDTO serviceDTO) {
        try {
            ServiceDTO createdService = serviceService.save(serviceDTO);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Serviço criado com sucesso", createdService));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao criar serviço: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ServiceDTO>> updateService(@PathVariable String id, @Valid @RequestBody ServiceDTO serviceDTO) {
        try {
            ServiceDTO updatedService = serviceService.update(id, serviceDTO);
            return ResponseEntity.ok(ApiResponse.success("Serviço atualizado com sucesso", updatedService));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao atualizar serviço: " + e.getMessage()));
        }
    }

    @PatchMapping("/{id}/toggle-active")
    public ResponseEntity<ApiResponse<ServiceDTO>> toggleServiceActive(@PathVariable String id) {
        try {
            ServiceDTO updatedService = serviceService.toggleActive(id);
            return ResponseEntity.ok(ApiResponse.success("Status do serviço alterado com sucesso", updatedService));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao alterar status do serviço: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteService(@PathVariable String id) {
        try {
            serviceService.deleteById(id);
            return ResponseEntity.ok(ApiResponse.success("Serviço excluído com sucesso", null));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao excluir serviço: " + e.getMessage()));
        }
    }
}
