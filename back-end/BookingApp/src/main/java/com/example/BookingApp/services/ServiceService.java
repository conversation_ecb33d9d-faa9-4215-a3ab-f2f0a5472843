package com.example.BookingApp.services;

import com.example.BookingApp.dto.ServiceDTO;
import com.example.BookingApp.entities.Service;
import com.example.BookingApp.entities.ServiceCategory;
import com.example.BookingApp.mappers.EntityMapper;
import com.example.BookingApp.repositories.ServiceCategoryRepository;
import com.example.BookingApp.repositories.ServiceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@org.springframework.stereotype.Service
@RequiredArgsConstructor
@Transactional
public class ServiceService {

    private final ServiceRepository serviceRepository;
    private final ServiceCategoryRepository serviceCategoryRepository;
    private final EntityMapper entityMapper;

    public List<ServiceDTO> findAll() {
        return serviceRepository.findAll()
                .stream()
                .map(entityMapper::toServiceDTO)
                .collect(Collectors.toList());
    }

    public List<ServiceDTO> findAllActive() {
        return serviceRepository.findByIsActiveTrue()
                .stream()
                .map(entityMapper::toServiceDTO)
                .collect(Collectors.toList());
    }

    public Optional<ServiceDTO> findById(String id) {
        return serviceRepository.findById(id)
                .map(entityMapper::toServiceDTO);
    }

    public List<ServiceDTO> findByCategoryId(String categoryId) {
        return serviceRepository.findByCategoryIdAndIsActiveTrue(categoryId)
                .stream()
                .map(entityMapper::toServiceDTO)
                .collect(Collectors.toList());
    }

    public List<ServiceDTO> searchByNameOrDescription(String search) {
        return serviceRepository.findByNameOrDescriptionContainingIgnoreCaseAndIsActiveTrue(search)
                .stream()
                .map(entityMapper::toServiceDTO)
                .collect(Collectors.toList());
    }

    public ServiceDTO save(ServiceDTO serviceDTO) {
        validateService(serviceDTO);
        
        Service service = entityMapper.toServiceEntity(serviceDTO);
        
        // Buscar a categoria pelo ID
        ServiceCategory category = serviceCategoryRepository.findById(serviceDTO.getCategory().getId())
                .orElseThrow(() -> new RuntimeException("Categoria não encontrada com ID: " + serviceDTO.getCategory().getId()));
        
        service.setCategory(category);
        Service savedService = serviceRepository.save(service);
        return entityMapper.toServiceDTO(savedService);
    }

    public ServiceDTO update(String id, ServiceDTO serviceDTO) {
        Service existingService = serviceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Serviço não encontrado com ID: " + id));

        validateServiceForUpdate(serviceDTO, id);

        existingService.setName(serviceDTO.getName());
        existingService.setDescription(serviceDTO.getDescription());
        existingService.setDuration(serviceDTO.getDuration());
        existingService.setPrice(serviceDTO.getPrice());
        existingService.setImage(serviceDTO.getImage());
        existingService.setIsActive(serviceDTO.getIsActive());

        if (serviceDTO.getCategory() != null && serviceDTO.getCategory().getId() != null) {
            ServiceCategory category = serviceCategoryRepository.findById(serviceDTO.getCategory().getId())
                    .orElseThrow(() -> new RuntimeException("Categoria não encontrada com ID: " + serviceDTO.getCategory().getId()));
            existingService.setCategory(category);
        }

        Service updatedService = serviceRepository.save(existingService);
        return entityMapper.toServiceDTO(updatedService);
    }

    public void deleteById(String id) {
        if (!serviceRepository.existsById(id)) {
            throw new RuntimeException("Serviço não encontrado com ID: " + id);
        }
        serviceRepository.deleteById(id);
    }

    public ServiceDTO toggleActive(String id) {
        Service service = serviceRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Serviço não encontrado com ID: " + id));
        
        service.setIsActive(!service.getIsActive());
        Service updatedService = serviceRepository.save(service);
        return entityMapper.toServiceDTO(updatedService);
    }

    private void validateService(ServiceDTO serviceDTO) {
        if (serviceDTO.getName() != null && serviceRepository.existsByName(serviceDTO.getName())) {
            throw new RuntimeException("Serviço já existe com o nome: " + serviceDTO.getName());
        }
    }

    private void validateServiceForUpdate(ServiceDTO serviceDTO, String serviceId) {
        // Implementar validação se necessário
    }
}
