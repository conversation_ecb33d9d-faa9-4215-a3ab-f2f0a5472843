package com.example.BookingApp.controllers;

import com.example.BookingApp.dto.ApiResponse;
import com.example.BookingApp.dto.ServiceCategoryDTO;
import com.example.BookingApp.services.ServiceCategoryService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/categories")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class ServiceCategoryController {

    private final ServiceCategoryService serviceCategoryService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<ServiceCategoryDTO>>> getAllCategories() {
        try {
            List<ServiceCategoryDTO> categories = serviceCategoryService.findAll();
            return ResponseEntity.ok(ApiResponse.success("Categorias encontradas com sucesso", categories));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar categorias: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<ServiceCategoryDTO>> getCategoryById(@PathVariable String id) {
        try {
            return serviceCategoryService.findById(id)
                    .map(category -> ResponseEntity.ok(ApiResponse.success("Categoria encontrada", category)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Categoria não encontrada")));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar categoria: " + e.getMessage()));
        }
    }

    @GetMapping("/name/{name}")
    public ResponseEntity<ApiResponse<ServiceCategoryDTO>> getCategoryByName(@PathVariable String name) {
        try {
            return serviceCategoryService.findByName(name)
                    .map(category -> ResponseEntity.ok(ApiResponse.success("Categoria encontrada", category)))
                    .orElse(ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(ApiResponse.error("Categoria não encontrada")));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao buscar categoria: " + e.getMessage()));
        }
    }

    @PostMapping
    public ResponseEntity<ApiResponse<ServiceCategoryDTO>> createCategory(@Valid @RequestBody ServiceCategoryDTO categoryDTO) {
        try {
            ServiceCategoryDTO createdCategory = serviceCategoryService.save(categoryDTO);
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("Categoria criada com sucesso", createdCategory));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao criar categoria: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<ServiceCategoryDTO>> updateCategory(@PathVariable String id, @Valid @RequestBody ServiceCategoryDTO categoryDTO) {
        try {
            ServiceCategoryDTO updatedCategory = serviceCategoryService.update(id, categoryDTO);
            return ResponseEntity.ok(ApiResponse.success("Categoria atualizada com sucesso", updatedCategory));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao atualizar categoria: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteCategory(@PathVariable String id) {
        try {
            serviceCategoryService.deleteById(id);
            return ResponseEntity.ok(ApiResponse.success("Categoria excluída com sucesso", null));
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao excluir categoria: " + e.getMessage()));
        }
    }

    @GetMapping("/exists/name/{name}")
    public ResponseEntity<ApiResponse<Boolean>> checkNameExists(@PathVariable String name) {
        try {
            boolean exists = serviceCategoryService.existsByName(name);
            return ResponseEntity.ok(ApiResponse.success("Verificação realizada", exists));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Erro ao verificar nome: " + e.getMessage()));
        }
    }
}
