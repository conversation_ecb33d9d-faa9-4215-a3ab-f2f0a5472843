package com.example.BookingApp.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BookingRequestDTO {
    @NotBlank(message = "ID do usuário é obrigatório")
    private String userId;

    @NotBlank(message = "ID do serviço é obrigatório")
    private String serviceId;

    @NotBlank(message = "ID do profissional é obrigatório")
    private String professionalId;

    @NotNull(message = "Data é obrigatória")
    private LocalDate date;

    @NotNull(message = "Horário é obrigatório")
    private LocalTime time;

    @Size(max = 500, message = "Observações devem ter no máximo 500 caracteres")
    private String notes;
}
