spring.application.name=BookingApp

# Configuração do banco de dados H2
spring.datasource.url=jdbc:h2:mem:bookingdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# Configuração do H2 Console (para desenvolvimento)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Configuração do JPA/Hibernate
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Configuração do servidor
server.port=8080

# Configuração de CORS
spring.web.cors.allowed-origins=http://localhost:3000,http://localhost:19006,exp://192.168.*:19000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# Configuração de timezone
spring.jpa.properties.hibernate.jdbc.time_zone=America/Sao_Paulo

# Configuração de logging
logging.level.com.example.BookingApp=DEBUG
logging.level.org.springframework.web=DEBUG

# Configuração JWT
jwt.secret=myVerySecretKeyForJWTTokenGenerationThatShouldBeAtLeast256BitsLong
jwt.expiration=86400000
