#!/bin/bash

echo "🔍 DIAGNÓSTICO DO BOOKINGAPP"
echo "================================"

# Verificar Java
echo "☕ Verificando Java..."
if command -v java &> /dev/null; then
    java -version
    echo "✅ Java encontrado"
else
    echo "❌ Java não encontrado!"
    echo "   Instale Java 21 ou superior"
fi

echo ""

# Verificar Maven
echo "📦 Verificando Maven..."
if [ -f "./mvnw" ]; then
    echo "✅ Maven wrapper encontrado"
    ./mvnw --version 2>/dev/null || echo "⚠️  Erro ao executar Maven wrapper"
else
    echo "❌ Maven wrapper não encontrado!"
fi

echo ""

# Verificar estrutura do projeto
echo "📁 Verificando estrutura do projeto..."
if [ -f "pom.xml" ]; then
    echo "✅ pom.xml encontrado"
else
    echo "❌ pom.xml não encontrado!"
fi

if [ -d "src/main/java" ]; then
    echo "✅ Diretório src/main/java encontrado"
else
    echo "❌ Diretório src/main/java não encontrado!"
fi

echo ""

# Verificar arquivos principais
echo "🔧 Verificando arquivos principais..."
files=(
    "src/main/java/com/example/BookingApp/BookingAppApplication.java"
    "src/main/resources/application.properties"
    "src/main/java/com/example/BookingApp/entities/User.java"
    "src/main/java/com/example/BookingApp/controllers/UserController.java"
    "src/main/java/com/example/BookingApp/services/UserService.java"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file não encontrado!"
    fi
done

echo ""

# Verificar porta 8080
echo "🌐 Verificando porta 8080..."
if lsof -i :8080 &> /dev/null; then
    echo "⚠️  Porta 8080 está em uso:"
    lsof -i :8080
else
    echo "✅ Porta 8080 está livre"
fi

echo ""

# Tentar compilar
echo "🔨 Tentando compilar..."
if ./mvnw clean compile -q &> /dev/null; then
    echo "✅ Compilação bem-sucedida"
else
    echo "❌ Erro na compilação!"
    echo "   Execute: ./mvnw clean compile"
    echo "   Para ver os erros detalhados"
fi

echo ""

# Verificar dependências problemáticas
echo "📋 Verificando dependências..."
if grep -q "spring-boot-starter-security" pom.xml; then
    echo "⚠️  Spring Security encontrado - pode causar problemas de autenticação"
fi

if grep -q "jjwt" pom.xml; then
    echo "⚠️  JWT encontrado - pode causar problemas se não configurado"
fi

echo ""

# Sugestões
echo "💡 SUGESTÕES:"
echo "1. Execute: ./mvnw spring-boot:run"
echo "2. Acesse: http://localhost:8080/api/categories"
echo "3. Console H2: http://localhost:8080/h2-console"
echo "4. Use o arquivo TESTE_MANUAL.md para testes detalhados"

echo ""
echo "🏁 Diagnóstico concluído!"
