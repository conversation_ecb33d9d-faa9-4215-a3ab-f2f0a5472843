# 🧪 Guia de Teste Manual - BookingApp

## 🚀 Como Executar a Aplicação

### 1. Executar a aplicação
```bash
cd back-end/BookingApp
./mvnw spring-boot:run
```

### 2. Verificar se está rodando
- A aplicação deve iniciar na porta 8080
- Você deve ver logs indicando que a aplicação iniciou
- Acesse: http://localhost:8080/h2-console para verificar o banco

## 🔍 Testes dos Endpoints

### 1. Testar Categorias de Serviços

#### Listar todas as categorias
```bash
curl -X GET http://localhost:8080/api/categories
```

**Resposta esperada:**
```json
{
  "success": true,
  "message": "Categorias encontradas com sucesso",
  "data": [
    {
      "id": "...",
      "name": "Cabelo",
      "description": "Serviços para cabelo",
      "icon": "cut",
      "color": "#FF6B6B"
    },
    ...
  ],
  "timestamp": "..."
}
```

### 2. Testar Serviços

#### Listar serviços ativos
```bash
curl -X GET http://localhost:8080/api/services/active
```

#### Buscar serviços por categoria
```bash
curl -X GET "http://localhost:8080/api/services/category/{categoryId}"
```

### 3. Testar Profissionais

#### Listar profissionais ativos
```bash
curl -X GET http://localhost:8080/api/professionals/active
```

### 4. Testar Usuários

#### Criar um novo usuário
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "name": "João Silva",
    "email": "<EMAIL>",
    "phone": "(11) 99999-0001"
  }'
```

#### Listar todos os usuários
```bash
curl -X GET http://localhost:8080/api/users
```

### 5. Testar Agendamentos

#### Verificar horários disponíveis
```bash
curl -X GET "http://localhost:8080/api/appointments/available-slots?professionalId={professionalId}&date=2024-01-15"
```

#### Criar um agendamento
```bash
curl -X POST http://localhost:8080/api/appointments \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "{userId}",
    "serviceId": "{serviceId}",
    "professionalId": "{professionalId}",
    "date": "2024-01-15",
    "time": "10:00",
    "notes": "Teste de agendamento"
  }'
```

## 🗄️ Verificar Banco de Dados H2

### Acessar Console H2
1. Acesse: http://localhost:8080/h2-console
2. Configure:
   - JDBC URL: `jdbc:h2:mem:bookingdb`
   - Username: `sa`
   - Password: `password`
3. Clique em "Connect"

### Consultas SQL para verificar dados

#### Verificar categorias
```sql
SELECT * FROM SERVICE_CATEGORIES;
```

#### Verificar serviços
```sql
SELECT s.*, sc.name as category_name 
FROM SERVICES s 
JOIN SERVICE_CATEGORIES sc ON s.category_id = sc.id;
```

#### Verificar profissionais
```sql
SELECT * FROM PROFESSIONALS;
```

#### Verificar usuários
```sql
SELECT * FROM USERS;
```

#### Verificar agendamentos
```sql
SELECT a.*, u.name as user_name, s.name as service_name, p.name as professional_name
FROM APPOINTMENTS a
JOIN USERS u ON a.user_id = u.id
JOIN SERVICES s ON a.service_id = s.id
JOIN PROFESSIONALS p ON a.professional_id = p.id;
```

## 🐛 Problemas Comuns e Soluções

### Erro: "Port 8080 already in use"
```bash
# Verificar o que está usando a porta
lsof -i :8080

# Matar o processo
kill -9 {PID}
```

### Erro: "Java not found"
```bash
# Verificar versão do Java
java -version

# Deve ser Java 21 ou superior
```

### Erro: "Maven not found"
```bash
# Usar o wrapper do Maven
./mvnw --version
```

### Erro de compilação
```bash
# Limpar e recompilar
./mvnw clean compile

# Se persistir, verificar dependências
./mvnw dependency:tree
```

## ✅ Checklist de Testes

- [ ] Aplicação inicia sem erros
- [ ] Console H2 acessível
- [ ] Dados iniciais carregados
- [ ] Endpoint de categorias funciona
- [ ] Endpoint de serviços funciona
- [ ] Endpoint de profissionais funciona
- [ ] Criação de usuário funciona
- [ ] Listagem de usuários funciona
- [ ] Verificação de horários disponíveis funciona
- [ ] Criação de agendamento funciona
- [ ] CORS configurado (sem erros de CORS no browser)

## 📊 Dados de Teste

### IDs dos dados iniciais (podem variar)
- Use as consultas SQL acima para obter os IDs reais
- Ou use os endpoints GET para listar e obter os IDs

### Exemplo de fluxo completo
1. Listar categorias → obter categoryId
2. Listar serviços da categoria → obter serviceId
3. Listar profissionais → obter professionalId
4. Criar usuário → obter userId
5. Verificar horários disponíveis
6. Criar agendamento

## 🔧 Logs Úteis

### Verificar logs da aplicação
```bash
tail -f logs/spring.log
```

### Habilitar logs de SQL
No `application.properties`:
```properties
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.SQL=DEBUG
```

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs da aplicação
2. Confirme que todas as dependências estão instaladas
3. Verifique se a porta 8080 está livre
4. Teste os endpoints um por um seguindo a ordem sugerida
