# BookingApp - Sistema de Agendamento

Sistema de agendamento para salão de beleza desenvolvido com Spring Boot, JPA e banco H2.

## 🚀 Tecnologias Utilizadas

- **Java 21**
- **Spring Boot 3.5.5**
- **Spring Data JPA**
- **H2 Database** (em memória)
- **Lombok**
- **Spring Validation**
- **Maven**

## 📋 Funcionalidades

### Entidades Principais
- **Usuários**: Clientes que fazem agendamentos
- **Categorias de Serviços**: Organização dos serviços (Cabelo, Unhas, Facial, etc.)
- **Serviços**: Serviços oferecidos pelo salão
- **Profissionais**: Funcionários que prestam os serviços
- **Agendamentos**: Reservas de horários

### Endpoints da API

#### Usuários (`/api/users`)
- `GET /api/users` - Listar todos os usuários
- `GET /api/users/{id}` - Buscar usuário por ID
- `GET /api/users/email/{email}` - Buscar usuário por email
- `POST /api/users` - Criar novo usuário
- `PUT /api/users/{id}` - Atualizar usuário
- `DELETE /api/users/{id}` - Excluir usuário

#### Categorias (`/api/categories`)
- `GET /api/categories` - Listar todas as categorias
- `GET /api/categories/{id}` - Buscar categoria por ID
- `POST /api/categories` - Criar nova categoria
- `PUT /api/categories/{id}` - Atualizar categoria
- `DELETE /api/categories/{id}` - Excluir categoria

#### Serviços (`/api/services`)
- `GET /api/services` - Listar todos os serviços
- `GET /api/services/active` - Listar serviços ativos
- `GET /api/services/{id}` - Buscar serviço por ID
- `GET /api/services/category/{categoryId}` - Serviços por categoria
- `GET /api/services/search?query={termo}` - Buscar serviços
- `POST /api/services` - Criar novo serviço
- `PUT /api/services/{id}` - Atualizar serviço
- `PATCH /api/services/{id}/toggle-active` - Ativar/desativar serviço
- `DELETE /api/services/{id}` - Excluir serviço

#### Profissionais (`/api/professionals`)
- `GET /api/professionals` - Listar todos os profissionais
- `GET /api/professionals/active` - Listar profissionais ativos
- `GET /api/professionals/{id}` - Buscar profissional por ID
- `GET /api/professionals/specialty/{specialty}` - Por especialidade
- `POST /api/professionals` - Criar novo profissional
- `PUT /api/professionals/{id}` - Atualizar profissional
- `PATCH /api/professionals/{id}/toggle-active` - Ativar/desativar
- `DELETE /api/professionals/{id}` - Excluir profissional

#### Agendamentos (`/api/appointments`)
- `GET /api/appointments` - Listar todos os agendamentos
- `GET /api/appointments/{id}` - Buscar agendamento por ID
- `GET /api/appointments/user/{userId}` - Agendamentos do usuário
- `GET /api/appointments/user/{userId}/upcoming` - Próximos agendamentos
- `GET /api/appointments/professional/{professionalId}` - Por profissional
- `GET /api/appointments/date/{date}` - Agendamentos por data
- `GET /api/appointments/available-slots?professionalId={id}&date={date}` - Horários disponíveis
- `POST /api/appointments` - Criar agendamento
- `PATCH /api/appointments/{id}/status?status={status}` - Atualizar status
- `PATCH /api/appointments/{id}/cancel` - Cancelar agendamento
- `DELETE /api/appointments/{id}` - Excluir agendamento

## 🛠️ Como Executar

### Pré-requisitos
- Java 21 ou superior
- Maven 3.6 ou superior

### Passos para execução

1. **Clone o repositório**
```bash
git clone <url-do-repositorio>
cd back-end/BookingApp
```

2. **Execute a aplicação**
```bash
./mvnw spring-boot:run
```

3. **Acesse a aplicação**
- API: http://localhost:8080/api
- Console H2: http://localhost:8080/h2-console
  - JDBC URL: `jdbc:h2:mem:bookingdb`
  - Username: `sa`
  - Password: `password`

## 📊 Dados Iniciais

A aplicação já vem com dados de exemplo:

### Categorias
- Cabelo
- Unhas
- Facial
- Massagem
- Maquiagem

### Serviços
- Corte de Cabelo (60min - R$ 80,00)
- Lavagem e Escova (45min - R$ 50,00)
- Coloração (120min - R$ 150,00)
- Manicure (45min - R$ 35,00)
- Pedicure (60min - R$ 40,00)
- Limpeza de Pele (90min - R$ 120,00)
- Massagem Relaxante (60min - R$ 100,00)
- Maquiagem Social (45min - R$ 80,00)

### Profissionais
- Maria Silva (Cabelo, Maquiagem)
- Ana Santos (Unhas, Facial)
- Carla Oliveira (Massagem, Facial)

## 🔧 Configurações

### Banco de Dados
O sistema usa H2 em memória por padrão. Para usar um banco persistente, altere as configurações em `application.properties`.

### CORS
Configurado para aceitar requisições de:
- localhost (qualquer porta)
- Expo Development Server
- 127.0.0.1

### Horários de Funcionamento
- Horários disponíveis: 9:00 às 18:00
- Intervalos: 30 minutos
- Domingos: fechado (configurável)

## 📝 Exemplo de Uso

### Criar um agendamento
```json
POST /api/appointments
{
  "userId": "user-id",
  "serviceId": "service-id",
  "professionalId": "professional-id",
  "date": "2024-01-15",
  "time": "10:00",
  "notes": "Observações opcionais"
}
```

### Verificar horários disponíveis
```
GET /api/appointments/available-slots?professionalId=prof-id&date=2024-01-15
```

## 🎯 Status dos Agendamentos

- `SCHEDULED` - Agendado
- `CONFIRMED` - Confirmado
- `IN_PROGRESS` - Em andamento
- `COMPLETED` - Concluído
- `CANCELLED` - Cancelado
- `NO_SHOW` - Não compareceu

## 🔍 Validações

- Emails únicos para usuários e profissionais
- Horários não conflitantes
- Dados obrigatórios validados
- Formatos de data e hora validados

## 📱 Integração com Front-end

A API foi desenvolvida para ser compatível com o front-end React Native/Expo, seguindo as interfaces TypeScript definidas no projeto front-end.
