#!/bin/bash

echo "🚀 Iniciando BookingApp..."
echo "📁 Diretório atual: $(pwd)"

# Verificar se o Java está instalado
if ! command -v java &> /dev/null; then
    echo "❌ Java não encontrado. Por favor, instale o Java 21 ou superior."
    exit 1
fi

echo "☕ Versão do Java:"
java -version

# Limpar e compilar
echo "🧹 Limpando e compilando..."
./mvnw clean compile -q

if [ $? -ne 0 ]; then
    echo "❌ Erro na compilação!"
    exit 1
fi

echo "✅ Compilação concluída com sucesso!"

# Executar a aplicação
echo "🚀 Iniciando a aplicação..."
./mvnw spring-boot:run

echo "🛑 Aplicação finalizada."
